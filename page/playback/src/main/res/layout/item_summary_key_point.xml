<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="@dimen/dp8">

    <View
        android:layout_width="@dimen/dp4"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp8"
        android:background="@drawable/shape_circle_primary" />

    <TextView
        android:id="@+id/text_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/Red30"
        android:textSize="@dimen/sp14"
        android:lineSpacingExtra="@dimen/dp2" />

</LinearLayout>
