<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_marginTop="8dp"
    android:layout_height="wrap_content">

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top|start"
        app:chipSpacingHorizontal="6dp"
        app:chipSpacingVertical="8dp" />

    <ViewStub
        android:id="@+id/chip_loading_layout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp28"
        android:layout="@layout/view_key_word_loading"
        android:layout_gravity="start|top"
        android:visibility="gone"
        tools:visibility="visible"
        />

</merge>