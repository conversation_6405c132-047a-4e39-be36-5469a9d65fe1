<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 空状态布局 -->
    <LinearLayout
        android:id="@+id/layout_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/dp24"
        android:visibility="visible">

        <ImageView
            android:layout_width="@dimen/dp80"
            android:layout_height="@dimen/dp80"
            android:layout_marginBottom="@dimen/dp16"
            android:src="@drawable/ic_summary_empty"
            android:contentDescription="@string/ai_summary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp8"
            android:text="@string/summary_empty_title"
            android:textColor="@color/Blue30"
            android:textSize="@dimen/sp16"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp24"
            android:gravity="center"
            android:text="@string/summary_empty_description"
            android:textColor="@color/Blue30"
            android:textSize="@dimen/sp14" />

        <com.coui.appcompat.button.COUIButton
            android:id="@+id/btn_generate_summary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/generate_summary" />

    </LinearLayout>

    <!-- 生成中状态布局 -->
    <LinearLayout
        android:id="@+id/layout_generating"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/dp24"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="@dimen/dp48"
            android:layout_height="@dimen/dp48"
            android:layout_marginBottom="@dimen/dp16"
            android:indeterminateTint="@color/Blue30" />

        <TextView
            android:id="@+id/text_generating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/summary_generating"
            android:textColor="@color/Blue30"
            android:textSize="@dimen/sp16" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:text="@string/summary_generating_tip"
            android:textColor="@color/Blue30"
            android:textSize="@dimen/sp14" />

    </LinearLayout>

    <!-- 内容显示布局 -->
    <LinearLayout
        android:id="@+id/layout_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone">

        <!-- 摘要头部信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp16"
            android:background="@color/Blue30">

            <TextView
                android:id="@+id/text_summary_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/recording_summary"
                android:textColor="@color/Blue30"
                android:textSize="@dimen/sp18"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/text_summary_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:textColor="@color/Blue30"
                android:textSize="@dimen/sp12" />

            <com.coui.appcompat.button.COUIButton
                android:id="@+id/btn_regenerate_summary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:text="@string/regenerate_summary"  />

        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp0_33"
            android:background="@color/coui_color_divider" />

        <!-- 摘要内容列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_summary"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="@dimen/dp16"
            android:clipToPadding="false" />

    </LinearLayout>

    <!-- 错误状态布局 -->
    <LinearLayout
        android:id="@+id/layout_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/dp24"
        android:visibility="gone">

        <ImageView
            android:layout_width="@dimen/dp80"
            android:layout_height="@dimen/dp80"
            android:layout_marginBottom="@dimen/dp16"
            android:src="@drawable/ic_summary_error"
            android:contentDescription="@string/error" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp8"
            android:text="@string/summary_error_title"
            android:textColor="@color/Blue30"
            android:textSize="@dimen/sp16"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/text_error_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp24"
            android:gravity="center"
            android:textColor="@color/Blue30"
            android:textSize="@dimen/sp14" />

        <com.coui.appcompat.button.COUIButton
            android:id="@+id/btn_retry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/retry" />

    </LinearLayout>

</FrameLayout>
