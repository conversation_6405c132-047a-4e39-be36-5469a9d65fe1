<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/divider_background_height"
        android:layout_gravity="center_horizontal"
        android:background="?attr/couiColorDivider"
        android:forceDarkAllowed="false"
        android:alpha="0"
        app:divider_depend_id="@id/convert_content"
        app:layout_behavior="com.soundrecorder.playback.newconvert.view.PlayBackConvertPrimaryTitleBehavior"
        android:importantForAccessibility="no" />

    <!--measureTextView is item_text_content in item_convert_content, must always be invisible-->
    <com.soundrecorder.playback.newconvert.view.BackgroundTextView
        android:id="@+id/measureTextView"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/recycler_view_item_marging_start_or_end"
        android:layout_marginEnd="@dimen/recycler_view_item_marging_start_or_end"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:layout_marginBottom="@dimen/dp14"
        android:textColor="@color/coui_color_primary_neutral"
        android:textSize="@dimen/sp16"
        android:includeFontPadding="false"
        android:textDirection="ltr"
        android:background="@color/coui_transparence"
        android:text="" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/converrt_content_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/recycler_view_item_marging_start_or_end"
        android:layout_marginEnd="@dimen/recycler_view_item_marging_start_or_end">

        <androidx.recyclerview.widget.COUIRecyclerView
            android:id="@+id/convert_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:scrollbars="none" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_space"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp40"
        android:layout_marginStart="@dimen/recycler_view_item_marging_start_or_end"
        android:layout_marginEnd="@dimen/recycler_view_item_marging_start_or_end"
        android:background="@drawable/gradient_convert_content"
        android:layout_gravity="bottom"
        android:alpha="0"
        android:clickable="false"
        android:focusable="false" />

    <RelativeLayout
        android:id="@+id/back_btn_layout"
        android:layout_width="@dimen/dp36"
        android:layout_gravity="bottom|end"
        android:layout_marginBottom="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp20"
        android:visibility="gone"
        android:background="@drawable/bg_back_button"
        android:layout_height="@dimen/dp36">
        <ImageView
            android:id="@+id/back_btn"
            android:layout_centerInParent="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_back_btn_up"/>
    </RelativeLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>