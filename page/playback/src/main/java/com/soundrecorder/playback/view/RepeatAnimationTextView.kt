/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: EffectiveAnimationTextView
 * Description:
 * Version: 1.0
 * Date: 2022/9/29
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/9/29 1.0 create
 */

package com.soundrecorder.playback.view

import android.animation.ValueAnimator
import android.content.Context
import android.content.res.Configuration
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.oplus.anim.EffectiveAnimationDrawable
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.R

open class RepeatAnimationTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : LinearLayout(context, attrs, defStyleAttr, defStyleRes) {

    private val mLogTag = "RepeatAnimationTextView"
    private var mAnimView: EffectiveAnimationView? = null
    private var mTextView: TextView? = null

    private var mAnimRawResLight = 0
    private var mAnimRawResNight = 0
    private var mDefaultImageRes = 0
    private var mUIMode = 0

    init {
        val attributeArray = context.obtainStyledAttributes(attrs, R.styleable.RepeatAnimationTextView, defStyleAttr, defStyleRes)
        mAnimRawResLight =
            attributeArray.getResourceId(R.styleable.RepeatAnimationTextView_anim_rawRes_light, 0)
        mAnimRawResNight =
            attributeArray.getResourceId(R.styleable.RepeatAnimationTextView_anim_rawRes_night, 0)
        mDefaultImageRes =
            attributeArray.getResourceId(R.styleable.RepeatAnimationTextView_default_image_res, 0)
        val animViewWidth = attributeArray.getDimension(
            R.styleable.RepeatAnimationTextView_animViewWidth,
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT.toFloat()
        ).toInt()
        val animViewHeight = attributeArray.getDimension(
            R.styleable.RepeatAnimationTextView_animViewHeight,
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT.toFloat()
        ).toInt()

        val bottomTextContent =
            attributeArray.getString(R.styleable.RepeatAnimationTextView_bottom_text)
        val bottomTextMarginTop =
            attributeArray.getDimension(R.styleable.RepeatAnimationTextView_bottom_text_margin_top, 0F)
        val bottomTextPaddingBottom = attributeArray.getDimension(
            R.styleable.RepeatAnimationTextView_bottom_text_paddingBottom,
            0F
        )
        val bottomTextPaddingHorizontal = attributeArray.getDimension(
            R.styleable.RepeatAnimationTextView_bottom_text_paddingHorizontal,
            0F
        )
        val bottomTextMinWidthStyle = attributeArray.getBoolean(
            R.styleable.RepeatAnimationTextView_bottom_text_minWidthStyle,
            false
        )
        attributeArray.recycle()

        mAnimView = EffectiveAnimationView(context).apply {
            this.repeatCount = ValueAnimator.INFINITE
            this.repeatMode = EffectiveAnimationDrawable.RESTART
            this.setCacheComposition(false)
            // 用于中大屏下占位，避免切换闪烁
            this.setImageResource(mDefaultImageRes)
        }
        val style = if (bottomTextMinWidthStyle) {
            R.style.textView_playBack_bottomTool_minWidth_force_dark
        } else {
            R.style.textView_playBack_bottomTool_force_dark
        }
        mTextView = TextView(context, null, 0, style).apply {
            text = bottomTextContent
            setPadding(
                bottomTextPaddingHorizontal.toInt(),
                paddingTop,
                bottomTextPaddingHorizontal.toInt(),
                bottomTextPaddingBottom.toInt()
            )
        }
        showAnimRawRes()
        addView(mAnimView, animViewWidth, animViewHeight)
        addView(
            mTextView,
            LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                topMargin = bottomTextMarginTop.toInt()
                gravity = Gravity.CENTER
            })
        orientation = VERTICAL
        gravity = Gravity.CENTER_HORIZONTAL
        mUIMode = context.resources.configuration.uiMode
        isForceDarkAllowed = false
    }

    private fun showAnimRawRes() {
        mAnimView?.setAnimation(mAnimRawResLight)
    }

    fun startRunAnim() {
        if (mAnimView?.isAnimating == true) {
            DebugUtil.i(mLogTag, "startRunAnim, cur is running")
            return
        }
//        showAnimRawRes()
        mAnimView?.setAnimation(mAnimRawResLight)
        mAnimView?.playAnimation()
    }

    private fun restartRunAnim() {
        if (mAnimView?.isAnimating == true) {
            DebugUtil.i(mLogTag, "restartRunAnim, cur is running")
            mAnimView?.cancelAnimation()
        }
        showAnimRawRes()
        mAnimView?.playAnimation()
    }

    fun showDefaultImage() {
        if (mAnimView?.isAnimating == true) {
            mAnimView?.cancelAnimation()
        }
        mAnimView?.setImageResource(mDefaultImageRes)
    }

    fun setBottomText(text: String?) {
        mTextView?.text = text
        contentDescription = text
    }

    fun setMaxWidth(maxWidth: Int) {
        mTextView?.maxWidth = maxWidth
    }

    fun setMinWidth(maxWidth: Int) {
        mTextView?.minWidth = maxWidth
    }

    override fun onDetachedFromWindow() {
        DebugUtil.i(mLogTag, "onDetachedFromWindow>>>> ")
        super.onDetachedFromWindow()
        mAnimView?.let {
            if (it.isAnimating) {
                it.cancelAnimation()
            }
        }
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        DebugUtil.i(mLogTag, "onVisibilityChanged,visibility $visibility ")
        super.onVisibilityChanged(changedView, visibility)
        if (mAnimView?.isAnimating == true) {
            if (visibility == VISIBLE) {
                mAnimView?.resumeAnimation()
            } else {
                mAnimView?.pauseAnimation()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        val isAnimating = mAnimView?.isAnimating == true
        super.onConfigurationChanged(newConfig)
        DebugUtil.i(mLogTag, "onConfigurationChanged mUIMode=$mUIMode, new ${newConfig?.uiMode}, isAnimating = $isAnimating")
        newConfig?.let {
            if (mUIMode != it.uiMode) {
                mUIMode = it.uiMode
                if (isAnimating) {
                    restartRunAnim()
                }
            }
        }
    }
}