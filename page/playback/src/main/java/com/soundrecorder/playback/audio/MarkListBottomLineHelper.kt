/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MarkListBottomLineHelper
 * Description:
 * Version: 1.0
 * Date: 2023/7/27
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/7/27 1.0 create
 */

package com.soundrecorder.playback.audio

import android.view.View
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.R

class MarkListBottomLineHelper(
    private var bottomLine: View?,
    private var bottomLinePanel: View?,
    private var markListView: RecyclerView?
) {
    private val logTag = "MarkListBottomLineHelper"
    private val location = IntArray(2)
    private var onScrollListener: RecyclerView.OnScrollListener? = null

    init {
        markListView?.run {
            onScrollListener = object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    handleBottomLineVisible("onScrolled")
                }
            }
            onScrollListener?.let { addOnScrollListener(it) }
        }
    }

    /**
     * 处理录制面板上方divider显示隐藏
     */
    fun handleBottomLineVisible(from: String) {
        val bottomLine = bottomLine ?: return
        val bottomPanel = bottomLinePanel ?: return
        val recyclerView = markListView ?: return
        if (!recyclerView.isVisible) {
            DebugUtil.d(logTag, "markView not visible,from $from")
            /**
             * 通过控制 isInvisible 控制显示隐藏，否则在有的场景下显示不出来：
             * 1.有足够的标记信息，小屏进入播放详情，底部线条显示不出来
             * 2.滚动标记列表到底部，在0秒处添加标记，底部线条显示不出来
             */
            bottomLine.isInvisible = true
            return
        }

        val totalCount = recyclerView.adapter?.itemCount ?: 0
        val lastVisiblePos =
            (recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
        if (lastVisiblePos != -1 && lastVisiblePos != (totalCount - 1)) {
            DebugUtil.d(
                logTag,
                "lastVisiblePos =$lastVisiblePos， totalCount=$totalCount,from $from"
            )
            // 最后一个可见item不是最后的item，则代表没滚动到最后，
            bottomLine.isVisible = true
            return
        }
        val lastItem = recyclerView.findViewHolderForLayoutPosition(totalCount - 1)?.itemView
            ?: return
        // 能拿到最后一个item，根据最后一个itemX和divider的X做比较，lastItem在面板下方，则显示divider，否则隐藏
        lastItem.getLocationInWindow(location)
        // 减去item底部space，item可见底部同divider接触才显示
        val lastItemY =
            location[1] + bottomLine.resources.getDimensionPixelOffset(com.soundrecorder.wavemark.R.dimen.mark_item_height)
        bottomPanel.getLocationInWindow(location)
        val dividerY = location[1]
        bottomLine.isInvisible = lastItemY <= dividerY

        DebugUtil.i(
            logTag,
            "handleBottomLineVisible lastItemY=$lastItemY, dividerY=$dividerY,from $from"
        )
    }

    fun onDestroyView() {
        bottomLine = null
        bottomLinePanel = null
        onScrollListener?.let { markListView?.removeOnScrollListener(it) }
        onScrollListener = null
        markListView = null
    }
}