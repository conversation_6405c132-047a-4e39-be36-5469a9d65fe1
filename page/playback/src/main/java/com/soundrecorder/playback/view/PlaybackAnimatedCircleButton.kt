/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlaybackAnimatedCircleButton
 * Description:
 * Version: 1.0
 * Date: 2022/12/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/12/12 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.common.widget.AnimatedCircleButton
import com.soundrecorder.playback.R

class PlaybackAnimatedCircleButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AnimatedCircleButton(context, attrs, defStyleAttr) {

    override var logTag: String = "PlaybackAnimatedCircleButton"

    var windowType: WindowType = WindowType.SMALL
        set(value) {
            field = value
        }

    private var mDrawablePlaySubWindow: Drawable? = null
    private var mDrawablePauseSubWindow: Drawable? = null

    /*默认按钮背景色*/
    private var circleColorDefault: Int = DEFAULT_CIRCLE_COLOR
    /*中大屏下，按钮背景色*/
    private var circleColorOverSmallWindow: Int = DEFAULT_CIRCLE_COLOR

    init {
        val typedArray = context.obtainStyledAttributes(
            attrs,
            com.soundrecorder.common.R.styleable.AnimatedCircleButton,
            defStyleAttr,
            0
        )
        circleColorDefault = typedArray.getColor(
            com.soundrecorder.common.R.styleable.AnimatedCircleButton_circle_color,
            DEFAULT_CIRCLE_COLOR
        )
        typedArray.recycle()
        circleColorOverSmallWindow =
            ContextCompat.getColor(context, R.color.play_button_background_sub_window)
    }

    override fun drawState(canvas: Canvas?) {
        canvas?.let {
            drawable?.apply { //scale offset
                val scaleY = height * (FLOAT_1 - mScaleCircle) / FLOAT_2
                val scaleX = width * (FLOAT_1 - mScaleCircle) / FLOAT_2 //reset draw bound
                val scaleTop = (top + scaleY).toInt()
                val scaleEnd = (right - scaleX).toInt()
                val scaleBottom = (bottom - scaleY).toInt()
                val scaleStart = (left + scaleX).toInt()
                bounds = Rect(scaleStart, scaleTop, scaleEnd, scaleBottom)
                draw(it)
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mDrawablePlaySubWindow = null
        mDrawablePauseSubWindow = null
    }
}