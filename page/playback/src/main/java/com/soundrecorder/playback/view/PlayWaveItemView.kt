/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayWaveItemView
 * Description:
 * Version: 1.0
 * Date: 2023/6/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/26 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import com.soundrecorder.playback.audio.PlayWaveRecyclerView
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.view.WaveItemView

class PlayWaveItemView(context: Context, attrs: AttributeSet?, defStyle: Int) : WaveItemView(context, attrs, defStyle) {

    companion object {
        private const val TAG = "PlayWaveItemView"
    }

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    override fun drawDottedLineWhenNoData(): Boolean {
        return true
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (isDrawDirectAmpBg()) {
            drawDirectAmpBg(canvas)
        }
    }

    private fun isDrawDirectAmpBg(): Boolean {
        return isEnhance || directTime?.isNotEmpty() == true
    }

    private fun drawDirectAmpBg(canvas: Canvas) {
        if (mViewIndex == 0 || parent == null || directTimeList == null) {
            return
        }

        if (parent is PlayWaveRecyclerView && directTimeList.isNotEmpty()) {
            /*0-6000,6000-12000,12000-18000*/
            val waveStartTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms
            val waveEndTime = mViewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms

            /*[1761-5417, 8513-14070, 19269-31326]*/
            var directStartTime = 0L
            var directEndTime = 0L
            for (directTime in directTimeList) {
                val startTime = directTime.startTime
                val endTime = directTime.endTime

                directStartTime = if (startTime >= waveStartTime) {
                    startTime
                } else { // startTime < waveStartTime
                    waveStartTime
                }
                directEndTime = if (endTime <= waveEndTime) {
                    endTime
                } else {
                    waveEndTime
                }

                drawDirectPlayAmpBg(directStartTime, directEndTime, waveStartTime, waveEndTime, canvas)
            }
        }
    }

    private fun drawDirectPlayAmpBg(
        directStartTime: Long,
        directEndTime: Long,
        waveStartTime: Long,
        waveEndTime: Long,
        canvas: Canvas
    ) {
        if (directStartTime in 0..directEndTime && directEndTime > 0) {
            var startX = if (directStartTime <= waveStartTime) getXByTime(waveStartTime) else getXByTime(directStartTime)
            var endX = if ((mViewIndex == mTotalCount - 1) || directEndTime <= waveEndTime) {
                getXByTime(directEndTime)
            } else {
                getXByTime(waveEndTime)
            }

            if (isReverseLayout) {
                startX = width - startX
                endX = width - endX
            }
//          DebugUtil.d(TAG, "drawDirectAmpBg directStartTime:$directStartTime, directEndTime:$directEndTime")
            mBackgroundPaint?.let {
                canvas.drawRect(
                    startX, mMarkViewHeight.toFloat(), endX,
                    mViewHeight, mBackgroundPaint
                )
            }
        }
    }
}