/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.content.Context
import android.util.DisplayMetrics
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil

class CustomLinearLayoutManager(context: Context) : LinearLayoutManager(context) {

    var multiple = 1
        set(value) {
            field = if (value < 1) {
                1
            } else {
                value
            }
        }
    var offset = 0
        set(value) {
            field = value
            DebugUtil.d(TAG, "CustomLinearLayoutManager offset = $offset")
        }

    var mCanScrollVertically = true
        set(value) {
            field = value
            DebugUtil.d(TAG, "CustomLinearLayoutManager mCanScrollVertically:$value")
        }

    override fun smoothScrollToPosition(recyclerView: RecyclerView?, state: RecyclerView.State?, position: Int) {
        val smoothScroller = SnapSmoothScroller(recyclerView!!.context)
        smoothScroller.targetPosition = position
        startSmoothScroll(smoothScroller)
    }

    override fun canScrollVertically(): Boolean {
        return mCanScrollVertically
    }

    inner class SnapSmoothScroller(context: Context) : LinearSmoothScroller(context) {

        //return scroll 1px time spent
        override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
            return (NUMBER_50F / displayMetrics.densityDpi) / multiple
        }

        override fun calculateDtToFit(
            viewStart: Int,
            viewEnd: Int,
            boxStart: Int,
            boxEnd: Int,
            snapPreference: Int
        ): Int {
            //自动滚到到1/5的高亮位置
            return (boxEnd - boxStart) / 5 - viewStart - offset
        }
    }

    companion object {
        private const val NUMBER_50F = 50F
        private const val TAG = "CustomLinearLayoutManager"
    }
}