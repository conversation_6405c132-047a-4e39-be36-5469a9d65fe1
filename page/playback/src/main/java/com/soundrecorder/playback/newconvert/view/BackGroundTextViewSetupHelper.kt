/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.view.View
import androidx.annotation.VisibleForTesting
import com.soundrecorder.playback.R
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.newconvert.ui.SeekPlayActionModeCallback

class BackGroundTextViewSetupHelper {

    companion object {
        const val TAG = "BackGroundTextViewSetupHelper"
    }

    interface OnBlackGroundTextClickListenner {
        fun onTextViewClick(
            view: View,
            convertContentItem: ConvertContentItem?,
            currentItemIndex: Int
        )
    }

    fun setUpBackgroundTextView(
        itemIndex: Int,
        currentItem: ConvertContentItem.TextItemMetaData?,
        convertContentItem: ConvertContentItem?,
        backgroundTextView: BackgroundTextView?,
        isFirstOne: Boolean,
        isLastOne: Boolean,
        searchMode: Boolean,
        callback: SeekPlayActionModeCallback?,
        onClickListener: OnBlackGroundTextClickListenner?,
        drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr
    ) {
        DebugUtil.i(TAG, "setUpBackgroundTextView isLastOne $isLastOne isFirstOne $isFirstOne, searchMode $searchMode")
        if (currentItem == null || convertContentItem == null || backgroundTextView == null) {
            DebugUtil.i(TAG, "checkAndAddBackgroundTextView currentItem or convertContentItem null or backgroundTextView null, return")
            return
        }
        ImageTextItemLayoutParamUtil.setTopMarginsForBackGroundTextView(
            isFirstOne,
            drawAttr,
            backgroundTextView
        )
        //设置标记下划线
        sentenceOfContentUnderLine(backgroundTextView, currentItem, convertContentItem, itemIndex)
        //设置text，同时设置标记小旗子
        setItemContent(backgroundTextView, currentItem)
        //设置正在播放的的高亮部分
        switchSentenceBackground(
            backgroundTextView,
            convertContentItem,
            currentItem,
            itemIndex,
            searchMode,
        )

        //设置TextView相关的Callback
        callback?.mItemTextView = backgroundTextView
        backgroundTextView.customSelectionActionModeCallback = callback
        DebugUtil.i(
            TAG,
            "checkAndAddBackgroundTextView addTextViewHeight ${backgroundTextView.measuredHeight}"
        )
        //设置TextView的点击事件处理
        if (FunctionOption.IS_SUPPORT_SEEKPLAY_FEATURE) {
            backgroundTextView.setOnClickListener {
                DebugUtil.i(TAG, "onTextViewClick")
                onClickListener?.onTextViewClick(it, convertContentItem, itemIndex)
            }
        }
        return
    }

    @VisibleForTesting
    fun sentenceOfContentUnderLine(
        backgroundTextView: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData,
        convertContentItem: ConvertContentItem,
        itemIndex: Int
    ) {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) return
        if (currentItem.textParagraph.isNullOrEmpty()) return
        if (currentItem.hasTextMark()) {
            val underline = mutableListOf<Pair<Int, Int>>()
            var count = 0
            for (sentencesIndex in currentItem.textParagraph!!.indices) {
                val subItem = currentItem.textParagraph?.get(sentencesIndex)
                if (subItem?.onlyHasSimpleMark == true) {
                    count++
                    val stringLengthBefore =
                        convertContentItem.getTextStringLengthBeforeTextImageItemIndex(itemIndex)
                    val space = count * TextImageMixLayout.SPACE.length  //当前段旗子所占用字符数
                    val startSeq = subItem.startCharSeq + space - stringLengthBefore
                    val endSeq =
                        subItem.endCharSeq + space - 1 - stringLengthBefore  //句尾不需要覆盖标点符号 减去标点符号所占用的字符数
                    DebugUtil.i(
                        TAG,
                        "sentenceOfContentUnderLine stringLengthBefore $stringLengthBefore, startSeq $startSeq, endSeq $endSeq"
                    )
                    underline.add(Pair(startSeq, endSeq))
                }
            }
            DebugUtil.i(
                TAG,
                "sentenceOfContentUnderLine   size = ${underline.size} underline == $underline"
            )
            if (underline.size > 0) {
                backgroundTextView.setIsUnderLine(underline, true)
            } else {
                backgroundTextView.setIsUnderLine(null, false)
            }
        } else {
            DebugUtil.i(
                TAG,
                "sentenceOfContentUnderLine hasMark = ${currentItem.hasTextMark()}" +
                        " listSubSentence is null or empty = ${currentItem.textParagraph.isNullOrEmpty()}"
            )
            backgroundTextView.setIsUnderLine(null, false)
        }
    }

    fun setItemContent(
        itemTextContent: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData?,
    ) {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) {
            itemTextContent.text = currentItem?.getTextString()
        } else {
            setItemContentSpannable(
                SpannableStringBuilder(currentItem?.getTextString()),
                itemTextContent,
                currentItem,
            )
        }
    }

    @VisibleForTesting
    fun setItemContentSpannable(
        builder: SpannableStringBuilder,
        itemTextContent: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData?,
        searchMode: Boolean = false
    ) {
        if (currentItem == null) {
            DebugUtil.i(TAG, "setItemContentSpannable item == null, return")
            return
        }
        if (currentItem.textParagraph != null && currentItem.textParagraph!!.isNotEmpty()) {
            var start = 0
            for (sentencesIndex in currentItem.textParagraph!!.indices) {
                val sentencesItem = currentItem.textParagraph!![sentencesIndex]
                if (sentencesItem.onlyHasSimpleMark) {
                    val imageSpan = CenterImageSpan(itemTextContent.context, R.drawable.ic_red_flag)
                    builder.insert(start, TextImageMixLayout.SPACE)
                    builder.setSpan(
                        imageSpan,
                        start,
                        start + TextImageMixLayout.IMAGE_SPAN_SPACE,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    //todo
                    if (searchMode && itemTextContent.getHighLight()) { // 本TextView中有关键词被选中，存在标题ICON的字符长度，需要矫正index
                        val originStart = itemTextContent.getStartHighLightSeq()
                        val originEnd = itemTextContent.getEndHighLightSeq()

                        if (originStart >= start) {  //在高亮词前插入ImageSpan，需要矫正高亮词Index
                            itemTextContent.updateHighLight(
                                originStart + TextImageMixLayout.SPACE.length,
                                originEnd + TextImageMixLayout.SPACE.length,
                                true,
                                invalidate = false
                            )
                            //DebugUtil.i(TAG, "setItemContentSpannable originStart ${originStart}, originEnd $originEnd ")
                        }
                    }
                    start += TextImageMixLayout.SPACE.length
                }
                start += sentencesItem.text.length
            }
        }
        DebugUtil.i(TAG, "setItemContentSpannable  text = ${builder.length}")
        itemTextContent.text = builder
    }

    /**
     * change focus state background by Sentence
     */
    fun switchSentenceBackground(
        backgroundTextView: BackgroundTextView,
        convertContentItem: ConvertContentItem?,
        currentItem: ConvertContentItem.TextItemMetaData?,
        itemIndex: Int,
        searchMode: Boolean
    ) {
        if (convertContentItem == null || currentItem == null) {
            return
        }
        //当前小段落为选中状态，设置为选中状态
        if (currentItem.isFocuse() && !searchMode) {
            var count = 0
            var startSeq: Int
            var endSeq = 0
            var space: Int
            for (subItem in currentItem.textParagraph!!) {
                if (subItem.onlyHasSimpleMark) {
                    val hasFlag = backgroundTextView.hasFlagSpan(endSeq, endSeq + TextImageMixLayout.SPACE.length)
                    DebugUtil.i(TAG, "switchSentenceBackground data has mark start:$endSeq view has flag：$hasFlag")
                    if (hasFlag) {
                        count++
                    }
                }
                space = count * TextImageMixLayout.SPACE.length  //当前段旗子所占用字符数
                startSeq = subItem.startCharSeq + space
                endSeq = subItem.endCharSeq + space
                if (subItem.isFocused) {
                    val textLengthBeforeIndex =
                        convertContentItem.getTextStringLengthBeforeTextImageItemIndex(itemIndex)
                    val showStartSeq = startSeq - textLengthBeforeIndex
                    val showEndSeq = endSeq - textLengthBeforeIndex
                    backgroundTextView.updateHighLight(showStartSeq, showEndSeq, true, invalidate = true)
                    DebugUtil.i(TAG, "switchSentenceBackground ItemIndex $itemIndex startSeq $startSeq, endSeq $endSeq" +
                            " , subItem.startCharSeq${subItem.startCharSeq}, subItem.endCharSeq ${subItem.endCharSeq}" +
                            ", space $space , textLengthBeforeIndex $textLengthBeforeIndex"
                    )
                }
            }
        } else {
            //当前整个item不是focus，设置为非高亮
            backgroundTextView.updateHighLight(0, 0, false, invalidate = true)
            backgroundTextView.background = null
        }
    }
}