/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import com.google.android.material.chip.ChipGroup
import com.soundrecorder.playback.R

class RestrictChipGroup @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ChipGroup(context, attrs, defStyleAttr) {

    private var mMaxHeight: Int = 0

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.RestrictChipGroup)
        mMaxHeight = typedArray.getLayoutDimension(R.styleable.RestrictChipGroup_max_height, mMaxHeight)
        typedArray.recycle()
    }

    @SuppressLint("RestrictedApi")
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var heightSpec = heightMeasureSpec
        if (mMaxHeight > 0) {
            heightSpec = MeasureSpec.makeMeasureSpec(mMaxHeight, MeasureSpec.AT_MOST)
        }
        super.onMeasure(widthMeasureSpec, heightSpec)
    }
}