/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description : 自定义SwitchPreference，主要添加自定义功能。
 * * 在数据未准备完成的时候，可以点击，有点击效果，但是文字是置灰的。
 * * 数据准备完成之后需要调用setDataReady刷新布局
 * * Version     : 1.0
 * * Date        : 2022/5/18
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.audio.setting

import android.content.Context
import android.util.AttributeSet
import android.widget.TextView
import androidx.preference.PreferenceViewHolder
import com.soundrecorder.playback.R
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.preference.COUISwitchPreference

class MySwitchPreference : COUISwitchPreference {
    private var mIsDataReady = false

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : this(context, attributeSet, androidx.preference.R.attr.switchPreferenceStyle)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) : this(context, attributeSet, defStyleAttr, 0)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(
        context,
        attributeSet,
        androidx.preference.R.attr.switchPreferenceStyle,
        defStyleRes
    ) {
        val obtainStyledAttributes =
            context.obtainStyledAttributes(attributeSet, R.styleable.PlaySettingSwitchPreference, defStyleAttr, defStyleRes)
        mIsDataReady = obtainStyledAttributes.getBoolean(R.styleable.PlaySettingSwitchPreference_isDataReady, false)
        obtainStyledAttributes.recycle()
    }

    override fun onBindViewHolder(holder: PreferenceViewHolder?) {
        super.onBindViewHolder(holder)
        (holder?.findViewById(android.R.id.title) as? TextView)?.apply {
            setTextColor(
                if (mIsDataReady) {
                    COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimaryNeutral)
                } else {
                    COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorDisabledNeutral)
                }
            )
        }
    }

    override fun onClick() {
        super.onClick()
        if (!mIsDataReady) {
            //如果数据没有准备好，则讲开关设置为关闭状态
            isChecked = false
        }
    }

    fun setDataReady(isReady: Boolean) {
        mIsDataReady = isReady
        notifyChanged()
    }
}