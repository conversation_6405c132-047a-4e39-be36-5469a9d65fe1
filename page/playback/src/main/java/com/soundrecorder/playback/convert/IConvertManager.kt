/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.convert

import android.content.DialogInterface
import android.content.res.Configuration
import android.view.View
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.PlaybackActivityViewModel

interface IConvertManager : LifecycleObserver {
    fun register(viewModelStoreOwner: Fragment, rootView: View, mediaId: Long, convertAbility: Int)
    fun setExportMenuItem()
    fun convertStartClickHandle(convertAiTitle: Boolean = false)
    fun setViewModel(viewModel: PlaybackActivityViewModel?)
    fun update(currentTimeMillis: Long)
    fun cancelConvert(@StringRes resId: Int)
    fun isContentExpanded(): Boolean
    fun roleControl(needShowRole: Boolean)
    fun stopScroll()
    fun addSpeakerTipTask(isOnConvertWhenViewPager2IDLE: () -> Boolean)
    fun removeSpeakerTipTask()
    fun updatePlayName()
    fun processExport(anchor: View?, dismissListener: DialogInterface.OnDismissListener?)
    fun processExportText(anchor: View?)
    fun showOrUnShowRoles(show: Boolean)
    fun setNeedShowAnimView(isNeedShow: Boolean)
    fun release()
    fun releaseView()
    fun releaseAnimView()
    fun cacheWindowShowing()
    fun checkNeedWindowShow(shareTxtAnchor: View?)
    fun getTimeString(): String
    fun getConvertContentData(): List<ConvertContentItem>?
    fun getConvertViewController(): IConvertViewController?
    fun doClickPermissionConvertOK() {}
    fun doClickPermissionConvertSearchOK() {}
    fun getConvertStatus(): MutableLiveData<Int>?
    fun onConfigurationChanged(newConfig: Configuration)
}