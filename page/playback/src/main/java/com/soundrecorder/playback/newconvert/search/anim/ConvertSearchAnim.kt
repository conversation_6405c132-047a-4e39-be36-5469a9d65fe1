/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search.anim

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.view.View
import android.view.ViewGroup
import androidx.annotation.FloatRange
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.updateLayoutParams
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.common.utils.ViewUtils.getUnDisplayViewHeight
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
import java.util.*
import java.util.function.Consumer

/**
 * 转文本搜索进入、退出动效
 * 进入时：标题栏的关键词列表消失，转文本内容向上移动
 * 退出时：标题栏的关键词列表显示，转文本内容向下移动
 */
class ConvertSearchAnim(
    private val view: KeyWordChipGroup,
    private val consumer: Consumer<Boolean>?
) {


    companion object {
        private const val TAG = "ConvertSearchAnim"

        // View动效的高度
        var animHeight = 0

        // 动效时长
        private const val DURATION = 400L

        // marginTop的值
        const val MARGIN_TOP = 16.0f

        /**
         * 将动效的高度重置，
         * 防止进入播放界面后，执行动效的参数是上一次进入的值
         */
        fun reset() {
            DebugUtil.d(TAG, "reset")
            animHeight = 0
        }
    }

    /**
     * 进入搜索时的动效
     */
    private var searchInAnim: ValueAnimator? = null
    private var inAnimSet: AnimatorSet = AnimatorSet()

    /**
     * 退出搜索时的动效
     */
    private var searchOutAnim: ValueAnimator? = null
    private var outAnimSet: AnimatorSet = AnimatorSet()

    /**
     * 是否已经执行进入动效
     */
    private var inAnimRan: Boolean = false


    /**
     * 进入搜索模式的动效
     */
    fun animateSearchIn() {
        if (inAnimRan) { // 已经执行过进入动效，则不再执行
            DebugUtil.e(TAG, "anim search in，has Ran")
            return
        }
        inAnimRan = true
        val viewHeight = getAnimHeight()
        DebugUtil.d(TAG, "anim search in from $viewHeight to 0")

        if (Objects.isNull(searchInAnim)) {
            searchInAnim = createHeightAnim(viewHeight, 0, true)
            val marginInAnim = createMarginTopAnim(ViewUtils.dp2px(MARGIN_TOP).toInt(), 0)
            val alphaAnim = createAlphaAnim(1.0f, 0f)
            inAnimSet.playTogether(searchInAnim, marginInAnim, alphaAnim)
        }
        searchInAnim?.setIntValues(viewHeight, 0)

        inAnimSet.let {
            if (it.isRunning) {
                it.cancel()
            }
            it.start()
        }
    }

    /**
     * 获取动效的高度
     * @return view的高度 + marginTop
     */
    private fun getAnimHeight(): Int {
        var height = animHeight
        if (height <= 0) { //说明没有保存高度
            // 获取view的高度
            height = view.height
        }
        if (height <= 0) { // 手动测量
            height = view.getUnDisplayViewHeight()
        }
        return height
    }

    /**
     * 退出搜索模式的动效
     */
    fun animateSearchOut() {
        val viewHeight = getAnimHeight()
        DebugUtil.d(TAG, "anim search out from 0 to $viewHeight")
        if (Objects.isNull(searchOutAnim)) {
            searchOutAnim = createHeightAnim(0, viewHeight, false)
            val marginAnim = createMarginTopAnim(0, ViewUtils.dp2px(MARGIN_TOP, true).toInt())
            val alphaAnim = createAlphaAnim(0f, 1.0f)
            outAnimSet.playTogether(searchOutAnim, marginAnim, alphaAnim)
        }
        searchOutAnim?.setIntValues(0, viewHeight)

        outAnimSet.let {
            if (it.isRunning) {
                it.cancel()
            }
            it.start()
        }
    }

    /**
     * 创建marginTop的动效
     */
    private fun createMarginTopAnim(from: Int, to: Int): ValueAnimator {
        val anim = ValueAnimator.ofInt(from, to)
        anim.duration = DURATION
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.addUpdateListener {
            val value = it.animatedValue as Int
            view.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                topMargin = value
            }
        }
        return anim
    }

    /**
     * 创建控件高度相关的动效
     * @param from 高度初始值
     * @param to 高度的结束值
     * @param inAnim 是否是进入动效
     */
    private fun createHeightAnim(from: Int, to: Int, inAnim: Boolean): ValueAnimator {
        val anim = ValueAnimator.ofInt(from, to)
        anim.duration = DURATION
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.addUpdateListener {
            val value = it.animatedValue as Int
//            DebugUtil.d(TAG, "anim value:$value")
            view.updateLayoutParams {
                height = value
            }
        }
        anim.doOnStart {
            if (!inAnim) {
                view.updateLayoutParams {
                    height = 0
                }
            }
            view.visibility = View.VISIBLE
        }
        anim.doOnEnd {
            view.visibility = if (inAnim) {
                View.GONE
            } else {
                View.VISIBLE
            }
            consumer?.accept(inAnim)
        }

        return anim
    }

    /**
     * 创建Alpha的动效
     * @param from 初始的alpha值
     * @param to 结束的alpha值
     */
    private fun createAlphaAnim(
        @FloatRange(from = 0.0, to = 1.0) from: Float,
        @FloatRange(from = 0.0, to = 1.0) to: Float
    ): ValueAnimator {
        val anim = ValueAnimator.ofFloat(from, to)
        anim.duration = DURATION
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.addUpdateListener {
            val value = it.animatedValue as Float
            view.alpha = value
        }
        return anim
    }

    fun release() {
        DebugUtil.e(TAG, "release")

        inAnimSet.let {
            if (it.isRunning) {
                it.cancel()
            }
        }

        outAnimSet.let {
            if (it.isRunning) {
                it.cancel()
            }
        }
    }

    /**
     * 判断动画是否正在执行
     */
    fun isRunning(): Boolean {
        return inAnimSet.isRunning || outAnimSet.isRunning
    }
}