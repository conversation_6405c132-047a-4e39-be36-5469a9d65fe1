/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search

import android.content.Context
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import com.soundrecorder.playback.newconvert.ui.TextImageItemAdapter
import com.soundrecorder.playback.newconvert.view.BackgroundTextView

class ConvertSearchAdapter(var inputContext: Context?) : TextImageItemAdapter(context = inputContext) {

    companion object {
        const val TAG = "ConvertSearchAdapter"
    }

    var mSearchScrollHelper: SearchScrollHelper? = null

    init {
        searchMode = true
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (data == null) return
        super.onBindViewHolder(holder, position)
    }

    @Suppress("TooGenericExceptionCaught")
    fun setConvertSearchFocus(focusPos: Int, lastPos: Int) {
        this.mConvertSearchCurrentFocus = focusPos
        val currentSearchFocus = searchHelper?.getSearchBean(focusPos) ?: return
        val lastSearchFocus = searchHelper?.getSearchBean(lastPos)
        val currentPosition = currentSearchFocus.textItemIndex
        DebugUtil.i(TAG, "setConvertSearchFocus, focusPos=$focusPos, lastFocusItem=$lastSearchFocus")
        try {
            if (lastSearchFocus != null) {
                val lastPosition = lastSearchFocus.textItemIndex
                if (lastPosition != currentPosition) {
                    notifyItemChanged(lastPosition + 1)
                }
            }
            notifyItemChanged(currentPosition + 1)
        } catch (e: Exception) {
            DebugUtil.d(TAG, e.message)
            notifyDataSetChanged()
        }
        mSearchScrollHelper?.setFocusSearchBean(currentSearchFocus)
        mSearchScrollHelper?.updateByKeyWord()
    }

    fun scrollToKeyWordPosition(focusPos: Int) {
        this.mConvertSearchCurrentFocus = focusPos
        val currentSearchFocus = searchHelper?.getSearchBean(focusPos) ?: return
        mSearchScrollHelper?.setFocusSearchBean(currentSearchFocus)
        mSearchScrollHelper?.updateByKeyWord()
    }

    override fun highlightSearchFocus(
        builder: SpannableStringBuilder,
        element: ConvertSearchBean,
        itemTextContent: BackgroundTextView
    ) {
        DebugUtil.i(
            TAG,
            "highlightSearchFocus executed. mConvertSearchCurrentFocus=$mConvertSearchCurrentFocus"
        )
        // 设置文字颜色为白色
        val keyWord = element.keyWord
        val keyWordIndex = element.keyWordIndex
        builder.setSpan(
            ForegroundColorSpan(Color.WHITE),
            keyWordIndex,
            keyWordIndex + keyWord.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        // 设置选中的背景颜色为红色
        itemTextContent.setBackgroundAlpha(255)
        itemTextContent.highlightSearchFocus(element)
    }

    override fun getTextHighlightType(): Int {
        return BackgroundTextView.HIGHLIGHT_TYPE_BACKGROUND
    }
}