/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.content.Context
import android.content.res.Resources
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.COUIRecyclerView
import com.soundrecorder.playback.R
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.ViewUtils.doOnLayoutChange

class PlayBackConvertPrimaryTitleBehavior(context: Context, attrs: AttributeSet? = null) :
    CoordinatorLayout.Behavior<View>(context, attrs) {
    private var mRecyclerView: COUIRecyclerView? = null
    private var mResources: Resources? = null
    private var mDividerLine: View? = null
    private val mLocation = IntArray(2)
    private var mListFirstChild: View? = null
    private var mListFirstChildInitY = 0

    private var dp30 = 0
    private var mDividerLineMargin = 0

    //recyclerviewView id
    private var resourceId: Int

    init {
        DebugUtil.d(TAG, "init")
        mResources = context.resources
        dp30 = mResources?.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp30) ?: 0
        mDividerLineMargin = mResources?.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.common_margin) ?: 0
        val obtainAttributes = context.resources.obtainAttributes(attrs, R.styleable.PlayConvertTitleDivider)
        resourceId = obtainAttributes.getResourceId(R.styleable.PlayConvertTitleDivider_divider_depend_id, -1)
        obtainAttributes.recycle()
    }

    override fun layoutDependsOn(parent: CoordinatorLayout, child: View, dependency: View): Boolean {
        DebugUtil.d(TAG, "dependency == $dependency")
        return findDependId(dependency) == resourceId
    }

    override fun onDependentViewChanged(parent: CoordinatorLayout, child: View, dependency: View): Boolean {
        DebugUtil.d(TAG, "onDependentViewChanged...")
        initDependencyView(child, dependency)
        return true
    }

    /**
     * initView
     */
    private fun initDependencyView(child: View, dependency: View) {
        if (mRecyclerView == null || mListFirstChild == null) {
            DebugUtil.d(TAG, "mRecyclerView == null...")
            val dependencyView = findDependView(dependency)
            if (dependencyView != null && dependencyView is COUIRecyclerView) {
                initView(child, dependencyView)
            }
        }
    }

    private fun findDependView(dependency: View): View? {
        if (dependency is COUIRecyclerView) {
            return dependency
        }
        val viewId = findDependId(dependency)
        return dependency.findViewById(viewId)
    }

    private fun findDependId(dependency: View): Int {
        var newDependencyId = dependency.id
        if (dependency is ConstraintLayout) {
            val recycleView = dependency.getChildAt(0)
            if (recycleView != null && recycleView is COUIRecyclerView) {
                newDependencyId = recycleView.id
            }
        }
        return newDependencyId
    }

    private fun initView(child: View, target: View) {
        DebugUtil.d(TAG, "initView...")
        mDividerLine = child
        initRecyclerViewLayoutParameters(target)
        findFirstChild()
        addListener()
    }

    private fun initRecyclerViewLayoutParameters(target: View) {
        mRecyclerView = target as COUIRecyclerView
        calculateFirstChildInitY()
        mRecyclerView?.doOnLayoutChange { _, _, _ ->
            calculateFirstChildInitY()
        }
    }

    /**
     * 如果不放在layoutChange中重新赋值
     * 五五分屏下，界面不触发重建，导致五五分屏交换位置后该值得不到更新，产生bug
     */
    private fun calculateFirstChildInitY() {
        mRecyclerView?.getLocationInWindow(mLocation)
        mListFirstChildInitY = mLocation[1] - dp30
        DebugUtil.d(TAG, "mListFirstChildInitY == $mListFirstChildInitY")
    }

    private fun addListener() {
        mOnScrollListener?.let {
            mRecyclerView?.addOnScrollListener(it)
        }
    }

    private var mOnScrollListener: androidx.recyclerview.widget.RecyclerView.OnScrollListener? =
        object : androidx.recyclerview.widget.RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: androidx.recyclerview.widget.RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                onListScroll()
            }
        }

    private fun onListScroll() {
        findFirstChild()
        updateDivider()
    }

    /**
     * 找到列表的第一个item，目前是header
     * 当第一个item的顶部距离 appBarLayout 为0的时候，设置divider的alpha逐渐显示出来
     * 知道-dp30的时候，完全显示
     * -dp30是header中 第一个textview的顶部间隔
     */
    private fun findFirstChild() {
        if (mListFirstChild == null) {
            DebugUtil.d(TAG, "mChild == null...")
            if (mRecyclerView is ViewGroup) {
                val viewGroup = mRecyclerView as ViewGroup
                if (viewGroup.childCount > 0) {
                    for (i in 0 until viewGroup.childCount) {
                        val childAt = viewGroup.getChildAt(i)
                        if (childAt.visibility == View.VISIBLE) {
                            mListFirstChild = childAt
                            childAt.getLocationInWindow(mLocation)
                            break
                        }
                    }
                }
            }
        }
    }

    /**
     * 更新divider的alpha
     * dp30是列表header中textview的marginTop
     * 将dp30作为一个过渡区，在此区间alpha从0f~1.0f渐变
     */
    private fun updateDivider() {
        mListFirstChild?.getLocationInWindow(mLocation)
        //获取当前位置到 appBarLayout 的间隔
        val offsetY = mLocation[1] - mListFirstChildInitY
        val alpha = when {
            offsetY >= dp30 -> {
                0f
            }
            offsetY > 0 -> {
                1 - offsetY.toFloat() / dp30
            }
            else -> {
                1f
            }
        }
        if (mDividerLine?.alpha != alpha) {
            mDividerLine?.apply {
                //ready param
                val param = layoutParams as? CoordinatorLayout.LayoutParams
                val marginStartAndEnd = (mDividerLineMargin * (1 - alpha)).toInt()
                param?.apply {
                    marginStart = marginStartAndEnd
                    marginEnd = marginStartAndEnd
                    //set param
                    layoutParams = this
                }
                this.alpha = alpha
            }
        }
//        DebugUtil.e(TAG, "alpha >> $alpha")
    }

    companion object {
        private const val TAG: String = "PlayBackConvertPrimaryTitleBehavior"
    }
}