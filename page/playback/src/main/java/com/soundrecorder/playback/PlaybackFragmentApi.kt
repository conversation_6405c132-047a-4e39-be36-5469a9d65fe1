/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlaybackFragmentApi
 * Description:
 * Version: 1.0
 * Date: 2023/1/5
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/1/5 1.0 create
 */

package com.soundrecorder.playback

import android.content.Intent
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.modulerouter.playback.PlaybackFragmentAction
import com.soundrecorder.playback.empty.PlayBackEmptyFragment

@Component(PlaybackFragmentAction.COMPONENT_NAME)
object PlaybackFragmentApi {

    @Action(PlaybackFragmentAction.ACTION_NEW_PLAYBACK_FRAGMENT)
    @JvmStatic
    fun newPlaybackFragment(showLoading: Boolean = true): Fragment {
        return PlaybackContainerFragment().apply {
            arguments = bundleOf(PlaybackContainerFragment.ARG_KEY_SHOW_LOADING to showLoading)
        }
    }

    @Action(PlaybackFragmentAction.ACTION_NEW_PLAYBACK_EMPTY_FRAGMENT)
    @JvmStatic
    fun newPlaybackEmptyFragment(): Fragment {
        return PlayBackEmptyFragment()
    }

    @Action(PlaybackFragmentAction.ACTION_ON_PERMISSION_GRANTED)
    @JvmStatic
    fun onPermissionGranted(fragment: Fragment?) {
        (fragment as? PlaybackContainerFragment)?.onPermissionGranted()
    }

    @Action(PlaybackFragmentAction.ACTION_ONSAVEINSTANCESTATE)
    @JvmStatic
    fun onSaveInstanceState(fragment: Fragment?, outState: Bundle) {
        (fragment as? PlaybackContainerFragment)?.getIPictureMarkDelegate()?.onSaveInstanceState(outState)
    }

    @Action(PlaybackFragmentAction.ACTION_ONRESTOREINSTANCESTATE)
    @JvmStatic
    fun onRestoreInstanceState(fragment: Fragment?, saveState: Bundle) {
        (fragment as? PlaybackContainerFragment)?.getIPictureMarkDelegate()?.onRestoreInstanceState(saveState)
    }

    @Action(PlaybackFragmentAction.ACTION_ON_NEWINTENT)
    @JvmStatic
    fun onNewIntent(fragment: Fragment?, intent: Intent?) {
        (fragment as? PlaybackContainerFragment)?.getIPictureMarkDelegate()?.onNewIntent(intent)
    }

    @Action(PlaybackFragmentAction.ACTION_ON_SET_REQUEST_CODE)
    @JvmStatic
    fun setRequestCodeX(fragment: Fragment?, code: Int) {
        (fragment as? PlaybackContainerFragment)?.getIPictureMarkDelegate()?.setRequestCodeX(code)
    }

    @Action(PlaybackFragmentAction.ACTION_ON_PRIVACY_POLICY_SUCCESS)
    @JvmStatic
    fun onPrivacyPolicySuccess(fragment: Fragment?, type: Int) {
        (fragment as? PlaybackContainerFragment)?.onPrivacyPolicySuccess(type)
    }

    @Action(PlaybackFragmentAction.ACTION_PAUSE_PLAY)
    @JvmStatic
    fun pausePlay(fragment: Fragment?, clearNotification: Boolean) {
        (fragment as? PlaybackContainerFragment)?.pausePlay(clearNotification)
    }
}