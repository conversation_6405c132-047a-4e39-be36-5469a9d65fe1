/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SelectSpeakerHelper
 * Description:
 * Version: 1.0
 * Date: 2025/3/14
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/14 1.0 create
 */

package com.soundrecorder.playback.newconvert.ui

import android.content.Context
import android.widget.TextView
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.R

class SelectSpeakerHelper(private val changeListener: ISelectSpeakerListener? = null) {

    companion object {
        @JvmStatic
        fun getSelectSpeakerName(speakers: List<String>): String {
            return if (speakers.isEmpty()) {
                BaseApplication.getAppContext().getString(R.string.all_speaker)
            } else {
                var name = ""
                speakers.forEachIndexed { index, speaker ->
                    name += if (index != speakers.size - 1) {
                        "$speaker、"
                    } else {
                        speaker
                    }
                }
                name
            }
        }
    }

    /*讲话人选择弹窗*/
    private var selectSpeakersPopup: COUIPopupListWindow? = null

    fun showChooseSpeakerDialog(context: Context, anchorView: TextView, selectSpeakers: MutableList<String>, allSpeakers: List<String>) {
        if (selectSpeakersPopup?.isShowing == true) {
            selectSpeakersPopup?.dismiss()
        }
        selectSpeakersPopup = COUIPopupListWindow(context)

        selectSpeakersPopup?.let {
            it.itemList = getItemList(selectSpeakers, allSpeakers)
            it.anchorView = anchorView
            it.resetOffset()
            it.setOnItemClickListener { _, _, pos, _ ->
                if (pos == 0) {
                    selectSpeakers.clear()
                } else {
                    allSpeakers.getOrNull(pos - 1)?.let {
                        if (!selectSpeakers.contains(it)) {
                            selectSpeakers.clear()
                            selectSpeakers.add(it)
                        }
                    }
                }
                anchorView.text = getSelectSpeakerName(selectSpeakers)
                changeListener?.onSpeakerSelect(selectSpeakers, selectSpeakers.size == allSpeakers.size)
                if (selectSpeakers.size <= NumberConstant.NUM_1) {
                    it.dismiss()
                } else {
                    it.itemList = getItemList(selectSpeakers, allSpeakers)
                }
            }
            it.show()
        }
    }

    private fun getItemList(selectSpeakers: MutableList<String>, allSpeakers: List<String>): List<PopupListItem> {
        return mutableListOf<PopupListItem>().apply {
            val builder = PopupListItem.Builder()
            val selectNone = selectSpeakers.isEmpty()
            val isSelectAll = selectSpeakers.size == allSpeakers.size && selectSpeakers.size > 1
            builder.setTitle(BaseApplication.getAppContext().getString(R.string.all_speaker))
                .setGroupId(R.id.group1)
                .setIsChecked(selectNone || isSelectAll)
            add(builder.build())

            allSpeakers.forEach {
                builder.setTitle(it).setGroupId(R.id.group3).setIsChecked(
                        if (isSelectAll) {
                            true
                        } else {
                            selectSpeakers.contains(it)
                        })
                add(builder.build())
            }
        }
    }

    fun release() {
        selectSpeakersPopup?.dismiss()
        selectSpeakersPopup = null
    }
}

interface ISelectSpeakerListener {
    /**
     * 勾选讲话人
     * @param speakers 被选中的讲话人名称
     * @param isSelectAll 手动勾选了讲话人列表的所有讲话人
     */
    fun onSpeakerSelect(speakers: List<String>, isSelectAll: Boolean)
}