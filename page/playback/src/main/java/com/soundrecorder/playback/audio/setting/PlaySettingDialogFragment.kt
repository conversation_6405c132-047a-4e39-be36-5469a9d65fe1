/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.audio.setting

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.preference.Preference
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.coui.appcompat.preference.COUIMarkPreference
import com.coui.appcompat.preference.COUIPreferenceFragment
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.R
import com.soundrecorder.playback.view.PlaySettingFooterPreference
import com.soundrecorder.playback.view.PlaySettingFooterPreference.OnBindOrClickListener
import oplus.multimedia.soundrecorder.playback.mute.MuteConstants.PLAY_INDEX_DEFAULT
import oplus.multimedia.soundrecorder.playback.mute.MuteDataState

class PlaySettingDialogFragment : COUIPanelFragment(),
    PlaySettingDialogPreferenceFragment.OnChoiceSelectedCallBack { //, LifecycleObserver

    companion object {
        private const val TAG = "PlaySettingDialogFragment"
        const val FRAGMENT_TAG = "PlaySettingDialogFragment"
    }

    private var mPreferenceDialog = PlaySettingDialogPreferenceFragment()
    private var mViewModel: PlaybackActivityViewModel? = null
    var menuItemComplete: MenuItem? = null
    private var currentSpeedIndex = 1
    private var muteEnabled = false
    private val mMuteEnableObserver by lazy {
        Observer<Boolean> {
            DebugUtil.d(TAG, "mMuteEnableObserver, value is $it")
            setSwitchChecked(it)
        }
    }

    private val mObserver = Observer<Int> {
        DebugUtil.d(TAG, "mObserver, value is $it")
        when (it) {
            MuteDataState.MUTE_LOAD_STATE_INIT,
            MuteDataState.MUTE_LOAD_STATE_PREPARING,
            MuteDataState.MUTE_LOAD_STATE_LOADING_FROM_CACHE,
            MuteDataState.MUTE_LOAD_STATE_LOADING_FROM_ORIGIN -> {
                setSwitchItemEnable(false)
            }

            MuteDataState.MUTE_LOAD_STATE_COMPLETED -> {
                setSwitchItemEnable(true)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 第一个parentFragment是dialogFragment，第二个是播放containerFragment
        parentFragment?.parentFragment?.let {
            mViewModel = ViewModelProvider(it)[PlaybackActivityViewModel::class.java]
        }
    }

    private fun initObserves() {
        mViewModel?.muteDataManager?.let { muteDataManager ->
            muteDataManager.getLoadingState()?.observe(viewLifecycleOwner, mObserver)
            muteDataManager.muteEnable.observe(viewLifecycleOwner, mMuteEnableObserver)
        }
        mPreferenceDialog.setOnChoiceSelectedCallBack(this)
        currentSpeedIndex = mViewModel?.playerController?.playSpeedIndex?.value ?: 1
        muteEnabled = mViewModel?.muteDataManager?.muteEnable?.value ?: false
    }


    override fun initView(panelView: View?) {
        super.initView(panelView)
        initObserves()
        initToolBar()
        initPreference()
        initDialogContent(panelView)
    }

    private fun initToolBar() {
        toolbar = toolbar?.apply {
//            visibility = View.INVISIBLE
            title = resources.getString(com.soundrecorder.common.R.string.play_setting_title)
            isTitleCenterStyle = true
            inflateMenu(R.menu.menu_cancel)
            menu.findItem(R.id.item_cancel_setting_dialog).apply {
                setOnMenuItemClickListener {
                    dismissDialog()
                    true
                }
            }
            menuItemComplete = menu.findItem(R.id.item_save_setting_dialog).apply {
                setOnMenuItemClickListener {
                    mViewModel?.muteDataManager?.apply {
                        if (getLoadingState()?.value == MuteDataState.MUTE_LOAD_STATE_COMPLETED) {
                            if (muteEnabled) {
                                enableMuteButton()
                            } else {
                                disableMuteEnable()
                            }
                            val state =
                                if (muteEnabled) RecorderUserAction.VALUE_SKIP_MUTE_OPEN else RecorderUserAction.VALUE_SKIP_MUTE_CLOSE
                            BuryingPoint.addSkipMuteSwitch(state)
                        } else {
                            enableMuteButton()
                        }
                    }
                    mViewModel?.playerController?.playSpeedIndex?.value = currentSpeedIndex
                    BuryingPoint.addMultipleSpeed(currentSpeedIndex)
                    dismissDialog()
                    true
                }
            }
        }
    }

    private fun initPreference() {
        childFragmentManager.beginTransaction().replace(contentResId, mPreferenceDialog).commit()
    }

    private fun initDialogContent(panelView: View?) {
        hideDragView()
    }

    private fun initStatus() {
        mViewModel?.let {
            mPreferenceDialog.setChoiceSelected(it.playerController.playSpeedIndex.value ?: 1)
            mPreferenceDialog.setSwitchChecked(it.muteDataManager?.muteEnable?.value == true)
        }
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        //设置dialogFragment的背景
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setPanelBackgroundTintColor(
            COUIContextUtil.getAttrColor(
                context,
                com.support.appcompat.R.attr.couiColorSurfaceWithCard
            )
        )
    }

    private fun setSwitchItemEnable(enable: Boolean) {
        mPreferenceDialog.setSwitchEnable(enable)
    }

    private fun setSwitchChecked(checked: Boolean) {
        mPreferenceDialog.setSwitchChecked(checked)
    }

    override fun onCheckMuteEnable(): Boolean {
        return mViewModel?.muteDataManager?.muteEnable?.value == true
    }

    override fun onSwitchChanged(checked: Boolean) {
        muteEnabled = checked
        setRestoreTextViewEnable()
    }

    override fun onSpeedChanged(position: Int) {
        currentSpeedIndex = position
        setRestoreTextViewEnable()
    }

    override fun onDefaultValueSelected() {
        setRestoreTextViewEnable()
    }

    override fun onPreferenceAdded() {
        when (mViewModel?.muteDataManager?.getLoadingState()?.value) {
            MuteDataState.MUTE_LOAD_STATE_COMPLETED -> {
                setSwitchItemEnable(true)
            }

            else -> {
                setSwitchItemEnable(false)
            }
        }
        initStatus()
    }

    private fun setRestoreTextViewEnable() {
        mPreferenceDialog.setRestoreTextViewEnable()
    }

    fun getTextViewEnable(): Boolean {
        return (currentSpeedIndex != PLAY_INDEX_DEFAULT || muteEnabled)
    }

    fun getCompleteBtnEnable(): Boolean {
        return (currentSpeedIndex != mViewModel?.playerController?.playSpeedIndex?.value ||
                muteEnabled != mViewModel?.muteDataManager?.muteEnable?.value)
    }

    fun onResetClick() {
        muteEnabled = false
        currentSpeedIndex = PLAY_INDEX_DEFAULT
        setRestoreTextViewEnable()
    }

    private fun dismissDialog() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    override fun onDestroy() {
        super.onDestroy()
        releaseInner()
    }

    private fun releaseInner() {
        mPreferenceDialog.setOnChoiceSelectedCallBack(null)
        mViewModel = null
    }
}

class PlaySettingDialogPreferenceFragment : COUIPreferenceFragment() {
    companion object {
        private const val TAG = "PlaySettingDialogFragment"
        private const val KEY_SWITCH = "play_setting_switch_preference"
        private const val KEY_SPEED_0_5 = "play_setting_speed_0_5_preference"
        private const val KEY_SPEED_1 = "play_setting_speed_1_preference"
        private const val KEY_SPEED_1_0_5 = "play_setting_speed_1_0_5_preference"
        private const val KEY_SPEED_2 = "play_setting_speed_2_preference"
        private const val KEY_FOOTER = "play_setting_footer_preference"
    }

    private var mSwitchPreference: MySwitchPreference? = null
    private var mSpeedChoiceHalf: COUIMarkPreference? = null
    private var mSpeedChoiceNormal: COUIMarkPreference? = null
    private var mSpeedChoiceOneDotFive: COUIMarkPreference? = null
    private var mSpeedChoiceTwice: COUIMarkPreference? = null
    private val mChoicesList = mutableListOf<COUIMarkPreference?>()
    private var mCallBack: OnChoiceSelectedCallBack? = null
    private var mCurrentSelectedPreference: COUIMarkPreference? = null
    private var mFooterPreference: PlaySettingFooterPreference? = null
    private var mRestoreTextView: TextView? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val onCreateView = super.onCreateView(inflater, container, savedInstanceState)
        //去掉原本的toolbar
        onCreateView?.let {
            (it as? ViewGroup)?.apply {
                removeView(it.findViewById(R.id.appbar_layout))
            }
        }
        return onCreateView
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        addPreferencesFromResource(R.xml.play_setting_dialog_preference)
        mSwitchPreference = findPreference(KEY_SWITCH)
        mSpeedChoiceHalf = findPreference(KEY_SPEED_0_5)
        mSpeedChoiceNormal = findPreference(KEY_SPEED_1)
        mSpeedChoiceOneDotFive = findPreference(KEY_SPEED_1_0_5)
        mSpeedChoiceTwice = findPreference(KEY_SPEED_2)
        mFooterPreference = findPreference(KEY_FOOTER)
        mFooterPreference?.setListener(object : OnBindOrClickListener {
            override fun onBind(textView: TextView) {
                mRestoreTextView = textView
                setRestoreTextViewEnable()
            }

            override fun onClick() {
                (parentFragment as? PlaySettingDialogFragment)?.onResetClick()
                restoreChoice()
            }
        })
        mChoicesList.add(mSpeedChoiceHalf)
        mChoicesList.add(mSpeedChoiceNormal)
        mChoicesList.add(mSpeedChoiceOneDotFive)
        mChoicesList.add(mSpeedChoiceTwice)
        mCurrentSelectedPreference = mSpeedChoiceNormal
        mCallBack?.onPreferenceAdded()
    }

    fun setRestoreTextViewEnable() {
        val menuItem = (parentFragment as? PlaySettingDialogFragment)?.menuItemComplete
        val resetEnable = (parentFragment as? PlaySettingDialogFragment)?.getTextViewEnable() ?: false
        val completeEnable = (parentFragment as? PlaySettingDialogFragment)?.getCompleteBtnEnable() ?: false
        mRestoreTextView?.isEnabled = resetEnable
        menuItem?.isEnabled = completeEnable
        if (resetEnable) {
            mRestoreTextView?.setTextColor(
                COUIContextUtil.getAttrColor(
                    context,
                    com.support.appcompat.R.attr.couiColorPrimaryText,
                    0
                )
            )
        } else {
            mRestoreTextView?.setTextColor(resources.getColor(com.support.appcompat.R.color.coui_color_disabled_neutral))
        }
        mRestoreTextView?.setBackgroundColor(Color.TRANSPARENT)
    }

    override fun onPreferenceTreeClick(preference: Preference?): Boolean {
        if (preference == null) {
            return super.onPreferenceTreeClick(preference)
        }
        when (preference) {
            mSwitchPreference -> {
                mCallBack?.onSwitchChanged(mSwitchPreference?.isChecked == true)
                checkDefaultSelected()
            }

            mSpeedChoiceHalf,
            mSpeedChoiceNormal,
            mSpeedChoiceOneDotFive,
            mSpeedChoiceTwice -> {
                setChoiceSelected(preference as COUIMarkPreference)
            }
        }

        return super.onPreferenceTreeClick(preference)
    }

    fun setSwitchEnable(enable: Boolean) {
        mSwitchPreference?.setDataReady(enable)
    }

    fun setSwitchChecked(checked: Boolean) {
        mSwitchPreference?.isChecked = checked
    }

    fun setChoiceSelected(position: Int) {
        mChoicesList.forEachIndexed { index, preference ->
            if (index == position) {
                preference?.isChecked = true
                mCurrentSelectedPreference = preference
            } else {
                preference?.isChecked = false
            }
        }
    }

    private fun setChoiceSelected(preference: COUIMarkPreference?) {
        if (preference == null) {
            return
        }
        mCurrentSelectedPreference = preference
        mChoicesList.forEach {
            it?.isChecked = preference == it
        }
        mCallBack?.onSpeedChanged(mChoicesList.indexOf(preference))
        checkDefaultSelected()
    }

    //检查当前的选项是否为默认选项，是则置灰“全部还原”按钮
    private fun checkDefaultSelected() {
        if (mSwitchPreference?.isChecked == false && mCurrentSelectedPreference == mSpeedChoiceNormal) {
            mCallBack?.onDefaultValueSelected()
        }
    }

    private fun restoreChoice() {
        mSwitchPreference?.isChecked = false
        mSpeedChoiceHalf?.isChecked = false
        mSpeedChoiceNormal?.isChecked = true
        mSpeedChoiceOneDotFive?.isChecked = false
        mSpeedChoiceTwice?.isChecked = false
        val position = mChoicesList.indexOf(mSpeedChoiceNormal)
        BuryingPoint.addMultipleSpeed(position)
        BuryingPoint.addSkipMuteSwitch(RecorderUserAction.VALUE_SKIP_MUTE_CLOSE)
        BuryingPoint.addRestoreAll()
    }

    fun setOnChoiceSelectedCallBack(callBack: OnChoiceSelectedCallBack?) {
        mCallBack = callBack
    }

    override fun onDestroy() {
        super.onDestroy()
        mCallBack = null
    }

    interface OnChoiceSelectedCallBack {
        fun onPreferenceAdded()

        fun onCheckMuteEnable(): Boolean

        fun onSwitchChanged(checked: Boolean)

        fun onSpeedChanged(position: Int)

        fun onDefaultValueSelected()
    }
}