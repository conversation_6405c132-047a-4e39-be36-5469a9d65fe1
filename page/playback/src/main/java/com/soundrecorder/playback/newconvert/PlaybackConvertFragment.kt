/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert

import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.doOnLayout
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.isInMultiWindowMode
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.common.flexible.FollowCOUIAlertDialog
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.TipUtil
import com.soundrecorder.common.utils.TipUtil.Companion.TYPE_ROLE
import com.soundrecorder.common.utils.TipUtil.Companion.TYPE_ROLE_NAME
import com.soundrecorder.common.utils.ViewUtils.setAnimatePressBackground
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.PlaybackContainerFragment
import com.soundrecorder.playback.R
import com.soundrecorder.playback.convert.IConvertManager
import com.soundrecorder.playback.databinding.FragmentPlaybackConvertBinding
import com.soundrecorder.playback.newconvert.convert.ConvertManagerImpl
import com.soundrecorder.summary.RecordSummaryManager
import com.soundrecorder.summary.util.SummarySupportManager
import java.util.Locale

class PlaybackConvertFragment : Fragment(), View.OnClickListener {

    companion object {
        const val TAG = "PlaybackConvertFragment"
        private const val DELAY_TIME = 300L
        private const val RESPONSE_IMMERSIVE_ANIMATION: Float = 0.16f
    }

    var mConvertManagerImpl: IConvertManager? = null

    private val mViewModel: PlaybackActivityViewModel by lazy {
        ViewModelProvider(requireParentFragment())[PlaybackActivityViewModel::class.java]
    }
    private val mConvertViewModel: PlaybackConvertViewModel by lazy {
        ViewModelProvider(requireParentFragment())[PlaybackConvertViewModel::class.java]
    }
    private var mBinding: FragmentPlaybackConvertBinding? = null
    private var bottomButtonHelper: PlaybackConvertBottomButtonHelper? = null
    // 切换沉浸态隐藏底部菜单栏动画
    private var immersiveLocateAnimation: COUISpringAnimation? = null
    private var immersiveAlphaAnimation: COUISpringAnimation? = null
    private var immersiveLocateFinal: Float = 0f
    private var isSelected = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DebugUtil.i(TAG, "onCreate $this , savedInstanceState $savedInstanceState")
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        DebugUtil.i(TAG, "onCreateView")
        mBinding = FragmentPlaybackConvertBinding.bind(inflater.inflate(R.layout.fragment_playback_convert, container, false))

        initConvertManager()
        initViewModelObserver()
        mBinding?.layoutConvertRole?.setOnClickListener(this)
        mBinding?.layoutConvertSearch?.setOnClickListener(this)
        mBinding?.layoutConvertSummary?.setOnClickListener(this)
        return mBinding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bottomButtonHelper = PlaybackConvertBottomButtonHelper()
        mConvertManagerImpl?.checkNeedWindowShow(getAnchorView())
        mBinding?.layoutConvertRole?.setAnimatePressBackground()
        mBinding?.layoutConvertSearch?.setAnimatePressBackground()
        mBinding?.layoutConvertExport?.setAnimatePressBackground()
        mBinding?.layoutConvertSummary?.setAnimatePressBackground()
        mBinding?.layoutConvertExport?.doOnLayout {
            bottomButtonHelper?.setPlaybackConvertBottomViewPosition(
                mBinding,
                mViewModel.mPanelShowStatus.value,
            )
        }
    }

    /**
     * 文本分享弹窗跟手锚点
     */
    private fun getAnchorView(): View? {
        if (!FollowCOUIAlertDialog.RECORDER_DIALOG_SUPPORT_FOLLOW) {
            DebugUtil.d(TAG, "getAnchorView: recorder not support follow")
            return null
        }
        return if (ScreenUtil.isBelowMiddleScreen()) {
            return findSmallConvertBtnView()
        } else {
            (parentFragment as? PlaybackContainerFragment)?.findConvertBtnView()
        }
    }

    private fun findSmallConvertBtnView(): View? {
        return mBinding?.root?.findViewById(R.id.layout_convert_export)
    }

    private fun initViewModelObserver() {
        mConvertViewModel.isSpeakerRoleShowing.observe(viewLifecycleOwner) { isSpeakerRoleShowing ->
            DebugUtil.i(TAG, "isShowConvertRole changeed $isSpeakerRoleShowing")
            mConvertManagerImpl?.roleControl(isSpeakerRoleShowing)
            mBinding?.layoutConvertRole?.setAnimateSelected(isSpeakerRoleShowing)
        }

        //need observe activity
        mViewModel.mPanelShowStatus.observe(viewLifecycleOwner) {
            /*沉浸态fragment中按钮不显示*/
            val innerConvertShowStatus = mViewModel.isImmersiveState.value != true && it.checkInnerConvertGroupShow()
            val hasSummary = it.checkHasSummary()
            setVisibleLayoutRoleAndExport(innerConvertShowStatus, it.mConvertShowSwitch, hasSummary)
        }

        mViewModel.playerController.currentTimeMillis.observe(viewLifecycleOwner) { currentTime ->
            mConvertManagerImpl?.update(currentTime)
        }

        mViewModel.playName.observe(viewLifecycleOwner) {
            mConvertManagerImpl?.updatePlayName()
        }

        mViewModel.mIsPageStopScroll.observe(viewLifecycleOwner) { isShowRoleNameTips ->
            mConvertManagerImpl?.getConvertViewController()?.setNeedShowRoleName(isShowRoleNameTips)
            if (isShowRoleNameTips) {
                mConvertManagerImpl?.addSpeakerTipTask { isOnConvertWhenViewPager2IDLE() }
            } else {
                mConvertManagerImpl?.removeSpeakerTipTask()
            }
        }

        mViewModel.mControlSpeakerTip.observe(viewLifecycleOwner) {
            if (it.checkInerRoleSpliteFirstTipNeedShow()) {
                addControlSpeakerTipTaskInner()
            } else {
                removeControlSpeakerTipTaskInner()
            }
        }

        // 恢复滚动位置
        mConvertViewModel.visibleItemLocation.observe(viewLifecycleOwner) {
            val layoutManager = mConvertManagerImpl?.getConvertViewController()?.getCustomLinearLayoutManager()
            layoutManager?.let {
                mConvertViewModel.restoreScrollPosition(it)
            }
        }
        // 摘要按钮
        mViewModel.mSummaryStatus.observe(viewLifecycleOwner) {
            val context = BaseApplication.getAppContext()
            when (it) {
                RecordSummaryManager.SUMMARY_STATE_CLIENT_END -> {
                    mBinding?.layoutConvertSummary?.let { view ->
                        view.setBottomText(context.resources.getString(com.soundrecorder.common.R.string.view_summary))
                        view.showDefaultImage()
                    }
                }

                RecordSummaryManager.SUMMARY_STATE_CLIENT_GENERATING -> {
                    mBinding?.layoutConvertSummary?.let { view ->
                        view.setBottomText(context.resources.getString(com.soundrecorder.common.R.string.summary_generating))
                        view.startRunAnim()
                    }
                }

                else -> {
                    mBinding?.layoutConvertSummary?.let { view ->
                        view.setBottomText(context.resources.getString(com.soundrecorder.common.R.string.generate_summary))
                        view.showDefaultImage()
                    }
                }
            }
            mViewModel.addShowSummaryEvent(
                SummaryStaticUtil.EVENT_FROM_CONVERT,
                SummarySupportManager.supportRecordSummary.value,
                it == RecordSummaryManager.SUMMARY_STATE_CLIENT_END)
        }

        mViewModel.isImmersiveState.observe(viewLifecycleOwner) {
            mBinding?.apply {
                if (it) {
                    //界面重建时需要等节布局绘制完成在切换到沉浸态
                    if (convertFlowLayout.width == 0) {
                        convertFlowLayout.post {
                            startAnimator()
                        }
                    } else {
                        startAnimator()
                    }
                } else {
                    reverse()
                }
            }
        }
    }

    private fun addControlSpeakerTipTaskInner() {
        DebugUtil.i(TAG, "addControlSpeakerTipTaskInner")
        TipUtil.checkShow(
            { if (isOnConvertWhenViewPager2IDLE()) mBinding?.layoutConvertRole else null },
            TYPE_ROLE,
            DELAY_TIME,
            lifecycle,
            isInMultiWindowMode()
        )
    }

    private fun removeControlSpeakerTipTaskInner() {
        TipUtil.dismissSelf(TYPE_ROLE)
    }

    fun isOnConvertWhenViewPager2IDLE(): Boolean {
        val playbackFragment = parentFragment as? PlaybackContainerFragment ?: return false
        val viewPager2IDLE = playbackFragment.isViewPager2IDLE()
        val onConvert = playbackFragment.isOnConvert()
        val fullConvertPage = isFullConvertPage()
        DebugUtil.d(TAG, "isOnConvertWhenViewPager2IDLE idle:$viewPager2IDLE onConvert:$onConvert fullPage:$fullConvertPage")
        return viewPager2IDLE && onConvert && fullConvertPage
    }

    private fun isFullConvertPage(): Boolean {
        val xy = intArrayOf(-1, -1)
        mBinding?.fragmentConvertRootView?.getLocationOnScreen(xy)
        DebugUtil.d(TAG, "isFullConvertPage,[${xy[0]},${xy[1]}]")
        if (xy[0] > 0 && ScreenUtil.getWindowType(resources.configuration) != WindowType.SMALL) {
            val maxWidth = resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.sub_window_parent_max_width)
            val defaultWidth = ScreenUtil.getRealScreenWidthContainSystemBars() * BrowseFileAction.getParentPercentDefault() + NumberConstant.NUM_F0_5
            val parentViewWidth = maxWidth.coerceAtMost(defaultWidth.toInt())
            DebugUtil.d(TAG, "isFullConvertPage, parentViewWidth = [$parentViewWidth]")
            // 中大屏减去父子级左侧列表宽度
            return (xy[0] - parentViewWidth) == 0
        }
        return xy[0] == 0
    }

    /**
     * 设置摘要，讲话人，内容搜索，分享文本四个按钮的显示和隐藏
     * @param layoutVisible 四个按钮的整个布局是否可见
     * @param roleVisible 讲话人按钮是否可见
     * @param hasSummary 摘要按钮是否可见
     */
    private fun setVisibleLayoutRoleAndExport(layoutVisible: Boolean, roleVisible: Boolean, hasSummary: Boolean) {
        DebugUtil.i(TAG, "setVisibleLayoutRoleAndExport layout: $layoutVisible role:$roleVisible")
        // 必须先设置flow的可见，否则flow的可见会覆盖掉子item的可见
        mBinding?.convertFlowLayout?.isVisible = layoutVisible
        // 讲话人
        mBinding?.layoutConvertRole?.isVisible = (layoutVisible && roleVisible)
        // 内容搜索
        mBinding?.layoutConvertSearch?.isVisible = (layoutVisible && FunctionOption.IS_SUPPORT_CONVERT_SEARCH)
        // 摘要
        mBinding?.layoutConvertSummary?.isVisible = (layoutVisible && hasSummary)
    }

    private fun initConvertManager() {
        mConvertManagerImpl = ConvertManagerImpl()
        if (mBinding?.root == null) {
            DebugUtil.e(TAG, "initConvertManager error")
            return
        }
        mConvertManagerImpl?.register(requireParentFragment(), mBinding?.root!!, mViewModel.recordId, mViewModel.convertSupportType)
        mConvertManagerImpl?.setExportMenuItem()
        mConvertManagerImpl?.setViewModel(mViewModel)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.layout_convert_role -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                mConvertViewModel.swithSpeakerRoleState()
            }
            R.id.layout_convert_search -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                // 记录滚动位置
                mConvertManagerImpl?.getConvertViewController()?.saveScrollPosition("convert_fragment")

                mConvertViewModel.also {
                    // 统计
                    it.clickConvertSearchCount++
                    // 进入转文本搜索界面
                    it.mIsInConvertSearch.value = true
                }
                BuryingPoint.addClickContentSearch()
            }
            R.id.layout_convert_summary -> {
                activity?.findViewById<View>(R.id.layout_summary_activity)?.callOnClick()
            }
        }
    }

    fun stopScroll() {
        val hasShow = TipUtil.hasShowTip(TYPE_ROLE_NAME)
        if (!hasShow) {
            mConvertManagerImpl?.stopScroll()
        }
    }

    override fun onDestroyView() {
        mConvertManagerImpl?.releaseView()
        super.onDestroyView()
    }

    override fun onDestroy() {
        DebugUtil.d(TAG, "onDestroy")
        /* 讲话人编辑次数埋点 */
        BuryingPoint.addRecordSpeakerEdit(
            mViewModel.recordId.toString(),
            RecorderUserAction.VALUE_EDIT_FROM, RecorderUserActionKt.sIsAppliedAll, RecorderUserActionKt.sEditCount)
        /*结束埋点 讲话人编辑次数 恢复默认值*/
        RecorderUserActionKt.sEditCount = 0
        mConvertManagerImpl?.cacheWindowShowing()
        mConvertManagerImpl?.release()
        super.onDestroy()
    }

    override fun onResume() {
        super.onResume()
        if (TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL) {
            bottomButtonHelper?.updatePlaybackConvertBottomWidth(
                mBinding,
                mViewModel.mPanelShowStatus.value
            )
        }
    }

    override fun onConfigurationChanged(p0: Configuration) {
        super.onConfigurationChanged(p0)
        mConvertManagerImpl?.onConfigurationChanged(p0)
    }

    private fun initImmersiveAnimation() {
        mBinding?.apply {
            //菜单栏隐藏动画，设置margin为负的布局高度实现隐藏，直接使用TRANSLATION_Y动画无效，与ConstraintLayout有关
            immersiveLocateAnimation =
                COUISpringAnimation(convertFlowLayout, COUIDynamicAnimation.ALPHA)
            immersiveLocateFinal = -convertFlowLayout.height.toFloat()
            val springSeekBarMargin = COUISpringForce()
                .setFinalPosition(immersiveLocateFinal)
                .setBounce(0f)
                .setResponse(RESPONSE_IMMERSIVE_ANIMATION)
            immersiveLocateAnimation?.setStartValue(1f)
            immersiveLocateAnimation?.setSpring(springSeekBarMargin)
            immersiveLocateAnimation?.addUpdateListener { _, value, _ ->
                convertFlowLayout.apply {
                    val params = layoutParams as MarginLayoutParams
                    params.bottomMargin = value.toInt()
                    setLayoutParams(params)
                }
            }

            //透明度变化
            immersiveAlphaAnimation =
                COUISpringAnimation(convertFlowLayout, COUIDynamicAnimation.ALPHA)
            val springAlpha = COUISpringForce()
                .setFinalPosition(0f)
                .setBounce(0f)
                .setResponse(RESPONSE_IMMERSIVE_ANIMATION)
            immersiveAlphaAnimation?.setSpring(springAlpha)
        }
    }

    /**
     * 切换沉浸态隐藏底部菜单栏动画
     */
    private fun startAnimator() {
        DebugUtil.d(TAG, "startAnimator")
        if (immersiveLocateAnimation == null) {
            initImmersiveAnimation()
        } else {
            immersiveLocateAnimation?.spring?.finalPosition = immersiveLocateFinal
            immersiveAlphaAnimation?.spring?.finalPosition = 0f
        }
        immersiveLocateAnimation?.start()
        immersiveAlphaAnimation?.start()
    }

    /**
     * 退出沉浸态显示底部菜单栏动画
     */
    private fun reverse() {
        DebugUtil.d(TAG, "reverse")
        if (immersiveLocateAnimation == null) {
            DebugUtil.d(TAG, "reverse  immersiveLocateAnimation is null")
            return
        }
        immersiveLocateAnimation?.animateToFinalPosition(1f)
        immersiveAlphaAnimation?.animateToFinalPosition(1f)
    }

    fun onPageSelectedStateChange(select: Boolean) {
        isSelected = select
    }
}