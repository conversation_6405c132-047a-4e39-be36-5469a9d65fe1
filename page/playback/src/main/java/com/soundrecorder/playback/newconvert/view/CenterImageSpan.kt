/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Paint.FontMetricsInt
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.text.style.DynamicDrawableSpan
import android.text.style.ImageSpan
import java.lang.ref.WeakReference

class CenterImageSpan(context: Context, resourceId: Int) : ImageSpan(context, resourceId, DynamicDrawableSpan.ALIGN_CENTER) {

    companion object {
        val TAG = "CenterImageSpan"
    }

    private var mDrawableRef: WeakReference<Drawable>? = null

    override fun getSize(paint: Paint, text: CharSequence?, start: Int, end: Int, fontMetricsInt: FontMetricsInt?): Int {
        val drawable: Drawable = drawable
        val rect: Rect = drawable.bounds
        if (fontMetricsInt != null) {
            val fmPaint = paint.fontMetricsInt
            //文字行高
            val fontHeight = fmPaint.descent - fmPaint.ascent
            //图片高度
            val drHeight = rect.bottom - rect.top

            val centerY = fmPaint.ascent + fontHeight / 2
            fontMetricsInt.ascent = centerY - drHeight / 2
            fontMetricsInt.top = fontMetricsInt.ascent
            fontMetricsInt.bottom = centerY + drHeight / 2
            fontMetricsInt.descent = fontMetricsInt.bottom
        }
        return rect.right
    }

    override fun draw(canvas: Canvas, text: CharSequence?, start: Int, end: Int, x: Float, top: Int, y: Int, bottom: Int, paint: Paint) {
        val drawable = getCachedDrawable()
        drawable?.let {
            canvas.save()
            val fmPaint = paint.fontMetricsInt
            val fontHeight = fmPaint.descent - fmPaint.ascent
            val centerY = y + fmPaint.descent - fontHeight / 2
            val transY: Int = centerY - (it.bounds.bottom - it.bounds.top) / 2
            canvas.translate(x, transY.toFloat())
            it.draw(canvas)
            canvas.restore()
        }
    }

    private fun getCachedDrawable(): Drawable? {
        val wr: WeakReference<Drawable>? = mDrawableRef
        var drawable: Drawable? = null
        if (wr != null) {
            drawable = wr.get()
        }
        if (drawable == null) {
            drawable = getDrawable()
            mDrawableRef = WeakReference(drawable)
        }
        return drawable
    }
}