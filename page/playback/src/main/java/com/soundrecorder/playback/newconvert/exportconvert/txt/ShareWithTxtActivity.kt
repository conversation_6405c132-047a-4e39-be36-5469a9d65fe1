/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.R

class ShareWithTxtActivity : BaseActivity() {
    companion object {
        const val TAG = "ShareWithTxtActivity"
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "ShareTxt"
    }

    private var mViewModel: ShareWithTxtViewModel? = null
    private var mShareWithTxtFragment: ShareWithTxtFragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_share_with_txt)
        //mediaRecordId
        val mediaRecordId = intent.extras?.getLong("mediaRecordId")
        DebugUtil.e(TAG, "mediaRecordId >> $mediaRecordId")

        mViewModel = ViewModelProvider(this).get(ShareWithTxtViewModel::class.java)

        attachFragments(savedInstanceState)
    }

    override fun navigationBarColor(): Int {
        return R.color.share_txt_navigation_color
    }

    private fun attachFragments(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            // do not create Fragment again when activity recreated with configuration changed
            return
        }
        DebugUtil.i(TAG, "attachFragments")
        val bundle = initBundle()
        val supportFragmentManager = supportFragmentManager
        supportFragmentManager.beginTransaction()
            .add(
                R.id.fragment_container_view,
                ShareWithTxtFragment::class.java,
                bundle,
                "ShareWithTxtFragment"
            )
            .setReorderingAllowed(true)
            .commit()
        mShareWithTxtFragment = getShareWithTxtFragment()
    }

    private fun getShareWithTxtFragment(): ShareWithTxtFragment? {
        val findFragment = supportFragmentManager.findFragmentByTag("ShareWithTxtFragment")
        return if (findFragment is ShareWithTxtFragment) {
            findFragment
        } else mShareWithTxtFragment
    }

    private fun initBundle(): Bundle {
        var mediaRecordId: Long = 0
        var canShowSpeaker = false
        var isShowSpeaker = false
        var createTime: Long = -1
        var playFileName = ""
        var playFilePath = ""

        intent?.extras?.let {
            mediaRecordId = it.getLong("mediaRecordId")
            canShowSpeaker = it.getBoolean("canShowSpeaker")
            isShowSpeaker = it.getBoolean("isShowSpeaker")
            createTime = it.getLong("createTime")
            playFileName = it.getString("playFileName", "")
            playFilePath = it.getString("playFilePath", "")
        }
        val bundle = Bundle()
        bundle.putLong("mediaRecordId", mediaRecordId)
        bundle.putBoolean("canShowSpeaker", canShowSpeaker)
        bundle.putBoolean("isShowSpeaker", isShowSpeaker)
        bundle.putLong("createTime", createTime)
        bundle.putString("playFileName", playFileName)
        bundle.putString("playFilePath", playFilePath)
        return bundle
    }
}