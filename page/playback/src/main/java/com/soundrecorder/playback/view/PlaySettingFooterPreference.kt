/*
Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
File: - PlaySettingFooterPreference
Description: 录音播放设置弹窗底部文案.
Version: 1.0
Date : 2024/1/16
Author: W9012748
--------------------- Revision History: ---------------------
<author>	<data> 	  <version >	   <desc>
W9012748  2024/1/16     1.0      create this file
*/
package com.soundrecorder.playback.view

import android.content.Context
import android.util.AttributeSet
import android.widget.TextView
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.preference.COUIPreference
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.soundrecorder.playback.R

class PlaySettingFooterPreference : COUIPreference {

    private var onBindOrClickListener: OnBindOrClickListener? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    override fun onBindViewHolder(holder: PreferenceViewHolder?) {
        super.onBindViewHolder(holder)
        val textView = holder?.itemView?.findViewById<TextView>(R.id.tv_play_setting_restore_all)
        textView?.let {
            COUITextViewCompatUtil.setPressRippleDrawable(it)
            onBindOrClickListener?.onBind(it)
            it.setOnClickListener {
                onBindOrClickListener?.onClick()
            }
        }
    }

    fun setListener(onBindOrClickListener: OnBindOrClickListener) {
        this.onBindOrClickListener = onBindOrClickListener
    }

    interface OnBindOrClickListener {
        fun onBind(textView: TextView)
        fun onClick()
    }
}