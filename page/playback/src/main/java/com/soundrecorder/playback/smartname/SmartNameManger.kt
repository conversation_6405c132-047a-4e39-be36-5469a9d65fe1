/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameServiceManger
 * * Description: SmartNameServiceManger
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.playback.smartname

import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.suffix
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.R
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.convertservice.convert.ConvertCheckUtils
import com.soundrecorder.convertservice.smartname.SmartNameTaskManager
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SmartNameManger() {

    companion object {
        private const val TAG = "SmartNameServiceManger"
        private var manager: SmartNameManger? = null
        fun getInstance(): SmartNameManger {
            if (manager == null) {
                synchronized(SmartNameManagerImpl::class.java) {
                    manager = SmartNameManger()
                }
            }
            return manager!!
        }
    }

    private var mSelectRecordList: MutableList<Record>? = null

    private var mSmartCallback: ISmartNameCallback? = object : ISmartNameCallback {

        override fun onSmartNameError(mediaId: Long, errorCode: Int, errorMsg: String?) {
            DebugUtil.d(TAG, "processSmartNameError, mediaId:$mediaId, errorCode:$errorCode, errorMsg:$errorMsg")
            unregisterSmartNameCallback(mediaId)
        }

        override fun onSmartNameFinished(
            mediaId: Long,
            jsonResult: String,
            extras: Map<String, Any>?
        ) {
            DebugUtil.d(TAG, "processSmartNameFinished, mediaId:$mediaId")
            unregisterSmartNameCallback(mediaId)
        }
    }

    private fun unregisterSmartNameCallback(mediaId: Long) {
        mSmartCallback?.let { SmartNameTaskManager.unregisterSmartNameCallback(mediaId, it) }
    }

    fun toastMessage(resId: Int) {
        ToastManager.showShortToast(BaseApplication.getAppContext(), resId)
    }

    /**
     * 开始智能命名/转文本
     */
    fun startOrResumeConvertSmartName(selectedMediaIdList: MutableList<Long>?) {
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        DebugUtil.d(TAG, "startOrResumeConvertSmartName, selectedMediaIdList:${selectedMediaIdList.size}")
        val idList = arrayOfNulls<String?>(selectedMediaIdList.size)
        selectedMediaIdList.forEachIndexed { index, id ->
            idList[index] = id.toString()
        }
        CoroutineScope(Dispatchers.IO).launch {
            mSelectRecordList = MediaDBUtils.getMediaRecordsById(BaseApplication.getAppContext(), idList)
            mSelectRecordList?.forEach {
                val mediaId = it.id
                val canConvert = checkCanConvert(it)
                DebugUtil.d(TAG, "startOrResumeConvertSmartName, canConvert:$canConvert")
                if (canConvert) {
                    mSmartCallback?.let { SmartNameTaskManager.registerSmartNameCallback(mediaId, it) }
                    SmartNameTaskManager.startSmartName(mediaId, null)
                }
            }
        }
    }

    /**
     * 文件后缀是否支持智能命名
     * 智能命名支持格式：MP3、AMR、AWB、AAC、WAV
     */
    fun isSuffixSupport(suffix: String?): Boolean {
        DebugUtil.d(TAG, "isSuffixSupport, suffix:$suffix")
        if (suffix.isNullOrEmpty()) {
            return false
        }
        return when (suffix) {
            RecorderConstant.MP3_FILE_SUFFIX,
            RecorderConstant.AAC_FILE_SUFFIX,
            RecorderConstant.WAV_FILE_SUFFIX,
            RecorderConstant.AMR_FILE_SUFFIX,
            RecorderConstant.AMR_WB_FILE_SUFFIX -> true
            else -> false
        }
    }

    private fun checkCanConvert(mediaRecord: Record): Boolean {
        val mediaId = mediaRecord.id
        val context = BaseApplication.getAppContext()
        if (NetworkUtils.isNetworkInvalid(context)) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
            return false
        }
        DebugUtil.i(TAG, "CheckConvertTask start. record:$mediaRecord")
        val suffix = mediaRecord.data.suffix()
        if (!isSuffixSupport(suffix)) {
            toastMessage(R.string.smart_name_error_damage_file)
            return false
        }
        var fileFormat: String? = null
        var fileDuration: Long? = null
        val fileSize: Long? = mediaRecord.mFileSize

        if (PermissionUtils.hasReadAudioPermission()) {
            ConvertDbUtil.updateRecordIdByMediaPath(mediaRecord.data, mediaId)
        }
        val convertRecord = ConvertDbUtil.selectByRecordId(mediaId)
        if (convertRecord == null) {
            DebugUtil.i(TAG, "MediaMetadataRetriever start.}")
            val mLocalUri = MediaDBUtils.genUri(mediaId)
            val pairFormatAndDuration = MediaDBUtils.getFileFormatAndDurationFromUri(mLocalUri)
            fileFormat = pairFormatAndDuration.first
            fileDuration = pairFormatAndDuration.second
            DebugUtil.i(TAG, "MediaMetadataRetriever end. mFileFormat:$fileFormat, mFileDuration:$fileDuration")
        }

        fileSize?.let {
            if (!ConvertCheckUtils.isFileSizeMinMet(it)) {
                DebugUtil.d(TAG, "isFileConditionMet, upload_status_exception")
                toastMessage(R.string.smart_name_error_damage_file)
                return false
            }
            if (!ConvertCheckUtils.isFileSizeMaxMet(fileSize)) {
                toastMessage(R.string.smart_name_error_size_long)
                return false
            }
        }

        if (!isFileDurationMet(mediaId, fileDuration)) {
            return false
        }
        if (!isFileFormatMet(mediaId, fileFormat)) {
            return false
        }
        return true
    }

    private fun isFileFormatMet(
        mediaId: Long,
        fileFormat: String?
    ): Boolean {
        fileFormat?.let {
            return ConvertCheckUtils.isFileFormatMet(it).apply {
                if (!this) {
                    //mSmartCallback?.onSmartNameError(mediaId, SmartNameAction.FILE_FORMAT_UNSUPPORT)
                    toastMessage(R.string.smart_name_error_format)
                }
            }
        }
        return true
    }

    private fun isFileDurationMet(
        mediaId: Long,
        fileDuration: Long?
    ): Boolean {
        fileDuration?.let {
            if (!ConvertCheckUtils.isFileDurationMinMet(it)) {
                DebugUtil.w(TAG, "mFileDuration <= 0!")
                toastMessage(R.string.smart_name_error_damage_file)
                return false
            }
            if (!ConvertCheckUtils.isFileDurationMaxMet(it)) {
                DebugUtil.d(TAG, "isFileConditionMet, fileDuration max")
                toastMessage(R.string.smart_name_error_duration_long)
                return false
            }
        }
        return true
    }

    fun release() {
        //mSmartNameService == null
        mSelectRecordList?.clear()
        SmartNameTaskManager.releaseAll()
    }
}