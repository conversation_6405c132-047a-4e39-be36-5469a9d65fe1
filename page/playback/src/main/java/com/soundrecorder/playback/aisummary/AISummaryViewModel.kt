package com.soundrecorder.playback.aisummary

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.SmartSummaryResult
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 摘要数据管理ViewModel
 * 负责管理摘要数据的获取、存储、状态管理等
 */
class AISummaryViewModel : ViewModel() {

    companion object {
        private const val TAG = "SummaryViewModel"
    }

    // 数据仓库
    private val aiDataBase = AISummaryDataBaseManager.getInstance()

    // 当前录音ID
    private var currentRecordId: Long = -1

    // 摘要状态
    private val _summaryState = MutableLiveData<SummaryState>()
    val summaryState: LiveData<SummaryState> = _summaryState

    // 摘要内容
    private val _summaryContent = MutableLiveData<String?>()
    val summaryContent: MutableLiveData<String?> = _summaryContent

    // 摘要项列表
    private val _summaryItems = MutableLiveData<List<SummaryItem>>()
    val summaryItems: LiveData<List<SummaryItem>> = _summaryItems

    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // 是否已注册回调
    private var isCallbackRegistered = false

    // AI摘要回调
    private val aiSummaryCallback = object : IAISummaryCallback {

        override fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryStart: mediaId:$mediaId")
            if (mediaId == currentRecordId) {
                _summaryState.postValue(SummaryState.GENERATING)
            }
        }

        override fun onAISummaryFinished(mediaId: Long, jsonResult: String, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryFinished: mediaId=$mediaId, result length=${jsonResult.length}")
            if (mediaId == currentRecordId && jsonResult.isNotEmpty()) {
                processSummaryResult(jsonResult)
            }
        }

        override fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String?) {
            DebugUtil.e(TAG, "onAISummaryError: mediaId=$mediaId, errorCode=$errorCode, errorMsg=$errorMsg")
            if (mediaId == currentRecordId) {
                handleSummaryError(errorCode, errorMsg)
            }
        }

        override fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryStop: mediaId=$mediaId, extras=$extras")
            if (mediaId == currentRecordId) {
                _summaryState.postValue(SummaryState.STOPPED)
            }
        }

        override fun onAISummaryEnd(mediaId: Long) {
            DebugUtil.d(TAG, "onAISummaryEnd: mediaId=$mediaId")
        }
    }

    /**
     * 摘要状态枚举
     */
    enum class SummaryState {
        EMPTY,      // 空状态，显示生成按钮
        GENERATING, // 生成中
        COMPLETED,  // 生成完成
        ERROR,      // 生成失败
        STOPPED     // 已停止
    }

    /**
     * 设置当前录音ID并初始化
     */
    fun setRecordId(recordId: Long) {
        if (recordId == currentRecordId) {
            DebugUtil.d(TAG, "setRecordId: recordId not changed, skip")
            return
        }

        DebugUtil.d(TAG, "setRecordId: $currentRecordId -> $recordId")

        // 注销旧的回调
        unregisterCallback()

        // 更新录音ID
        currentRecordId = recordId

        // 重置状态
        resetState()

        // 注册新的回调
        registerCallback()

        // 检查现有摘要
        checkExistingSummary()
    }

    /**
     * 生成摘要
     */
    fun generateSummary() {
        if (currentRecordId <= 0) {
            DebugUtil.w(TAG, "generateSummary: invalid recordId=$currentRecordId")
            _errorMessage.postValue("无效的录音ID")
            return
        }

        DebugUtil.d(TAG, "generateSummary: recordId=$currentRecordId")

        viewModelScope.launch {
            try {
                // 检查是否已经在生成中
                val isRunning = withContext(Dispatchers.IO) {
                    AISummaryAction.checkIsTaskRunning(currentRecordId)
                }

                if (isRunning) {
                    DebugUtil.w(TAG, "generateSummary: task already running")
                    _summaryState.postValue(SummaryState.GENERATING)
                    return@launch
                }

                // 更新状态为生成中
                _summaryState.postValue(SummaryState.GENERATING)

                // 启动摘要生成
                val success = withContext(Dispatchers.IO) {
                    AISummaryAction.startAISummary(currentRecordId)
                }

                if (!success) {
                    DebugUtil.e(TAG, "generateSummary: failed to start")
                    _summaryState.postValue(SummaryState.ERROR)
                    _errorMessage.postValue("启动摘要生成失败")
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "generateSummary: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("生成摘要时发生异常: ${e.message}")
            }
        }
    }

    /**
     * 重新生成摘要
     */
    fun regenerateSummary() {
        if (currentRecordId <= 0) {
            DebugUtil.w(TAG, "regenerateSummary: invalid recordId=$currentRecordId")
            return
        }

        DebugUtil.d(TAG, "regenerateSummary: recordId=$currentRecordId")

        viewModelScope.launch {
            try {
                // 停止当前任务
                withContext(Dispatchers.IO) {
                    if (AISummaryAction.checkIsTaskRunning(currentRecordId)) {
                        AISummaryAction.stopAISummaryTask(currentRecordId)
                    }
                }

                // 清除当前数据
                clearSummaryData()

                // 重新生成
                generateSummary()
            } catch (e: Exception) {
                DebugUtil.e(TAG, "regenerateSummary: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("重新生成摘要失败: ${e.message}")
            }
        }
    }

    /**
     * 停止摘要生成
     */
    fun stopSummary() {
        if (currentRecordId <= 0) {
            DebugUtil.w(TAG, "stopSummary: invalid recordId=$currentRecordId")
            return
        }

        DebugUtil.d(TAG, "stopSummary: recordId=$currentRecordId")

        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    AISummaryAction.stopAISummaryTask(currentRecordId)
                }
                _summaryState.postValue(SummaryState.STOPPED)
            } catch (e: Exception) {
                DebugUtil.e(TAG, "stopSummary: exception=${e.message}")
            }
        }
    }

    /**
     * 检查现有摘要
     */
    private fun checkExistingSummary() {
        if (currentRecordId <= 0) {
            return
        }

        viewModelScope.launch {
            try {
                // 检查是否正在生成
                val isRunning = withContext(Dispatchers.IO) {
                    AISummaryAction.checkIsTaskRunning(currentRecordId)
                }

                if (isRunning) {
                    _summaryState.postValue(SummaryState.GENERATING)
                } else {
                    // 从数据库加载已有的摘要数据
                    val existingSummary = aiDataBase.getSummary(currentRecordId)
                    if (existingSummary != null) {
                        DebugUtil.d(TAG, "checkExistingSummary: found existing summary")
                        loadExistingSummary(existingSummary.summaryContent)
                    } else {
                        DebugUtil.d(TAG, "checkExistingSummary: no existing summary found")
                        _summaryState.postValue(SummaryState.EMPTY)
                    }
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "checkExistingSummary: exception=${e.message}")
                _summaryState.postValue(SummaryState.EMPTY)
            }
        }
    }

    /**
     * 加载已有摘要
     */
    private fun loadExistingSummary(summaryContent: String) {
        viewModelScope.launch {
            try {
                // 保存摘要内容
                _summaryContent.postValue(summaryContent)

                // 解析摘要项
                val items = SummaryItem.parseContent(summaryContent)
                _summaryItems.postValue(items)

                // 更新状态
                _summaryState.postValue(SummaryState.COMPLETED)

                DebugUtil.d(TAG, "loadExistingSummary: success, items count=${items.size}")
            } catch (e: Exception) {
                DebugUtil.e(TAG, "loadExistingSummary: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("加载摘要失败: ${e.message}")
            }
        }
    }

    /**
     * 处理摘要结果
     */
    private fun processSummaryResult(jsonResult: String) {
        viewModelScope.launch {
            try {
                val summaryResult = GsonUtil.fromJson(jsonResult, SmartSummaryResult::class.java)
                val summaryContent = summaryResult?.lastSummary

                if (!summaryContent.isNullOrEmpty()) {
                    // 保存摘要内容
                    _summaryContent.postValue(summaryContent)

                    // 解析摘要项
                    val items = SummaryItem.parseContent(summaryContent)
                    _summaryItems.postValue(items)

                    // 更新状态
                    _summaryState.postValue(SummaryState.COMPLETED)

                    // 保存到数据库（使用默认摘要风格）
                    val saved = aiDataBase.saveSummary(currentRecordId, summaryContent, -1)
                    DebugUtil.d(TAG, "processSummaryResult: saved to database=$saved")

                    DebugUtil.d(TAG, "processSummaryResult: success, items count=${items.size}")
                } else {
                    DebugUtil.w(TAG, "processSummaryResult: empty content")
                    _summaryState.postValue(SummaryState.ERROR)
                    _errorMessage.postValue("摘要内容为空")
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "processSummaryResult: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("解析摘要结果失败: ${e.message}")
            }
        }
    }

    /**
     * 处理摘要错误
     */
    private fun handleSummaryError(errorCode: Int, errorMsg: String?) {
        val message = when (errorCode) {
            AISummaryAction.NETWORK_ERROR -> "网络连接失败，请检查网络设置"
            AISummaryAction.CONTENT_LESS_ERROR -> "录音内容较少，无法生成摘要"
            AISummaryAction.SERVER_ERROR -> "服务异常，摘要生成失败"
            AISummaryAction.REQUEST_TIMEOUT -> "请求超时，请重试"
            else -> errorMsg ?: "生成摘要失败，错误码：$errorCode"
        }

        _summaryState.postValue(SummaryState.ERROR)
        _errorMessage.postValue(message)
    }

    /**
     * 注册回调
     */
    private fun registerCallback() {
        if (currentRecordId > 0 && !isCallbackRegistered) {
            DebugUtil.d(TAG, "registerCallback: recordId=$currentRecordId")
            AISummaryAction.registerAISummaryCallback(currentRecordId, aiSummaryCallback)
            isCallbackRegistered = true
        }
    }

    /**
     * 注销回调
     */
    private fun unregisterCallback() {
        if (isCallbackRegistered && currentRecordId > 0) {
            DebugUtil.d(TAG, "unregisterCallback: recordId=$currentRecordId")
            AISummaryAction.unRegisterAISummaryCallback(currentRecordId, aiSummaryCallback)
            isCallbackRegistered = false
        }
    }

    /**
     * 重置状态
     */
    private fun resetState() {
        _summaryState.postValue(SummaryState.EMPTY)
        _summaryContent.postValue("")
        _summaryItems.postValue(emptyList())
        _errorMessage.postValue("")
    }

    /**
     * 清除摘要数据
     */
    private fun clearSummaryData() {
        _summaryContent.postValue("")
        _summaryItems.postValue(emptyList())
    }

    override fun onCleared() {
        super.onCleared()
        DebugUtil.d(TAG, "onCleared")
        unregisterCallback()
    }
}
