/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.util

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.common.databean.ConvertContentItem
import java.util.*
import kotlin.collections.ArrayList

object ListUtil {
    const val TAG = "ListUtil"

    @JvmStatic
    fun getDistinctRoleName(data: List<ConvertContentItem>?): MutableList<String> {
        val originList = ArrayList<String>()
        data?.let { list ->
            for (item in list) {
                item.roleName?.let {
                    originList.add(it)
                }
            }
        }
        return originList.distinct().toMutableList()
    }

    @JvmStatic
    fun getTimeString(recordId: Long?, playName: String?): String {
        val date = Date(
            RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                .getCreateTimeByPath(
                    recordId
                        ?: -1, playName?.endsWith(".amr") ?: false
                )
        )

        return RecorderICUFormateUtils.formatDateTimeShort(date)
    }

    @JvmStatic
    fun getMetaDataList(data: List<ConvertContentItem>?): MutableList<ConvertContentItem.ItemMetaData> {
        val originList = mutableListOf<ConvertContentItem.ItemMetaData>()
        data?.let { list ->
            for (item in list) {
                item.mTextOrImageItems?.let {
                    originList.addAll(it)
                }
            }
        }
        return originList
    }

    @JvmStatic
    fun getTextMetaDataList(data: List<ConvertContentItem>?): MutableList<Pair<Int, ConvertContentItem.TextItemMetaData>> {
        val result = mutableListOf<Pair<Int, ConvertContentItem.TextItemMetaData>>()
        var initPosition = 0
        data?.let { list ->
            for (item in list) {
                item.mTextOrImageItems?.forEachIndexed { index, metaItem ->
                    var position = initPosition
                    if (metaItem is ConvertContentItem.TextItemMetaData) { // 从搜索结果中匹配到item的位置,高亮搜索词
                        position += index
                        result.add(Pair(position, metaItem))
                    }
                }
                initPosition += (item.mTextOrImageItems?.size ?: 0)
            }
        }
        DebugUtil.i(TAG, "getTextMetaDataList input data $data, outputData $result")
        return result
    }

    @JvmStatic
    fun getTimeMetaDataList(data: List<ConvertContentItem>?): MutableList<Pair<Int, ConvertContentItem.TimerDividerMetaData>> {
        val result = mutableListOf<Pair<Int, ConvertContentItem.TimerDividerMetaData>>()
        var initPosition = 0
        data?.let { list ->
            for (item in list) {
                item.mTextOrImageItems?.forEachIndexed { index, metaItem ->
                    var position = initPosition
                    if (metaItem is ConvertContentItem.TimerDividerMetaData) { // 从搜索结果中匹配到item的位置,高亮搜索词
                        position += index
                        result.add(Pair(position, metaItem))
                    }
                }
                initPosition += (item.mTextOrImageItems?.size ?: 0)
            }
        }
        DebugUtil.i(TAG, "getTextMetaDataList input data $data, outputData $result")
        return result
    }
}