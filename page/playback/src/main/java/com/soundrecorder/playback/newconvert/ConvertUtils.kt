/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert

import android.content.Context
import android.content.Intent
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager
import com.soundrecorder.convertservice.convert.NewConvertTextService
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.summary.data.Sentence

object ConvertUtils {

    private const val TAG = "ConvertManager"

    @JvmStatic
    fun cancelAllConvertTask() {
        ConvertTaskThreadManager.cancelAllTask()
    }

    @JvmStatic
    fun stopConvertService(context: Context) {
        DebugUtil.i(TAG, "stop convert service.")
        context.stopService(Intent(context, NewConvertTextService::class.java))
    }

    @JvmStatic
    fun convertConvertContentItemToSentence(itemList: List<ConvertContentItem>?): List<Sentence>? {
        itemList ?: return null
        val resultList = mutableListOf<Sentence>()
        var sentence: Sentence
        itemList.forEach {
            sentence = Sentence().apply {
                sTime = it.startTime
                eTime = it.endTime
                roleId = it.roleId
                roleName = it.roleName ?: ""
                content = it.textContent
            }
            resultList.add(sentence)
        }
        return resultList
    }
}