/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackConvertBottomButtonHelper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert

import androidx.core.view.isVisible
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentPlaybackConvertBinding

class PlaybackConvertBottomButtonHelper {
    private val logTag = "PlaybackConvertBottomButtonHelper"

    companion object {
        private const val CONVERT_BOTTOM_BTN_SPACE = 5
    }

    /**
     * @param panelStatus 监听数据
     */
    fun setPlaybackConvertBottomViewPosition(
        bottomView: FragmentPlaybackConvertBinding?,
        panelStatus: PlaybackActivityViewModel.PanelShowStatus?
    ) {
        val binding = bottomView ?: return
        if (!binding.layoutConvertExport.isVisible) {
            return
        }
        val validWidth =
            bottomView.fragmentConvertRootView.width - 2 * bottomView.fragmentConvertRootView.resources.getDimensionPixelSize(
                R.dimen.play_layout_mark_marginHorizontal
            )
        val showCount = getShowCount(panelStatus)
        val minWidth = binding.layoutConvertExport.minWidth
        val maxWidth = validWidth / showCount - CONVERT_BOTTOM_BTN_SPACE
        DebugUtil.i(logTag, "setBottomViewMaxWidth maxWidth =$maxWidth  ,minWidth =$minWidth")
        if (minWidth > maxWidth) {
            binding.layoutConvertRole.minWidth = maxWidth
            binding.layoutConvertSearch.minWidth = maxWidth
            binding.layoutConvertExport.minWidth = maxWidth
            binding.layoutConvertSummary.setMinWidth(maxWidth)
        }
        if (maxWidth != binding.layoutConvertExport.maxWidth) {
            binding.layoutConvertRole.maxWidth = maxWidth
            binding.layoutConvertSearch.maxWidth = maxWidth
            binding.layoutConvertExport.maxWidth = maxWidth
            binding.layoutConvertSummary.setMaxWidth(maxWidth)
        }
    }

    private fun getShowCount(panelStatus: PlaybackActivityViewModel.PanelShowStatus?): Int {
        val convertShowSwitch = panelStatus?.mConvertShowSwitch == true
        // 支持摘要和讲话人四个按钮 不支持摘要或者讲话人三个按钮，都不支持两个按钮
        val hasSummary = panelStatus?.checkHasSummary() == true
        val showCount = if (convertShowSwitch && hasSummary) {
            NumberConstant.NUM_4
        } else if (!convertShowSwitch && !hasSummary) {
            NumberConstant.NUM_3
        } else {
            NumberConstant.NUM_2
        }
        return showCount
    }

    fun updatePlaybackConvertBottomWidth(
        bottomView: FragmentPlaybackConvertBinding?,
        panelStatus: PlaybackActivityViewModel.PanelShowStatus?
    ) {
        val binding = bottomView ?: return
        val showCount = getShowCount(panelStatus)
        val width = binding.convertFlowLayout.width / showCount
        binding.layoutConvertRole.width = width
        binding.layoutConvertSearch.width = width
        binding.layoutConvertExport.width = width
    }
}