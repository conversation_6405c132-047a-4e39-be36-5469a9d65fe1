/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.content.Context
import android.util.TypedValue
import com.soundrecorder.playback.R
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.imageload.utils.ImageParseResult

object ImageWithHeightCaculateUtil {


    const val TAG = "ImageWithHeightCaculateUtil"
    const val BIG_IMAGE_RATIO = 0.65F

    @Volatile
    var maxtWithAndRatio: ImageViewMaxtWithAndRatio? = null

    data class ImageViewShowConfig(
        var imageViewWidth: Int = 0,
        var imageViewHeight: Int = 0,
        var needFitCenter: Boolean = false
    )

    data class ImageViewMaxtWithAndRatio(var maxTextWidth: Int, var imageMaxWidthRatio: Float)

    @JvmStatic
    fun getImageViewMaxtWithAndRatio(context: Context): ImageViewMaxtWithAndRatio {
        var result: ImageViewMaxtWithAndRatio
        if (maxtWithAndRatio == null) {
            val maxTextWidth = initMaxTextWidth(context)
            val imageTextWidthRatio = initImageMaxWidthRatio(context)
            result = ImageViewMaxtWithAndRatio(maxTextWidth, imageTextWidthRatio)
            maxtWithAndRatio = result
        } else {
            result = maxtWithAndRatio!!
        }
        return result
    }

    @JvmStatic
    fun releaseMaxWithAndRatio() {
        maxtWithAndRatio = null
    }

    @JvmStatic
    private fun initMaxTextWidth(context: Context): Int {
        val ratioTypedValue = TypedValue()
        context.resources.getValue(R.dimen.screen_width_percent_parentchild, ratioTypedValue, true)
        val ratio = ratioTypedValue.float

        val padding = context.resources.getDimensionPixelSize(R.dimen.recycler_view_item_marging_start_or_end)
        val screenWidth = ScreenUtil.getRealScreenWidth()

        val result = screenWidth * ratio - 2 * padding
        DebugUtil.i(
            TAG,
            "initMaxTextWidth screenWidth $screenWidth, ratio $ratio , padding $padding, result $result"
        )
        return result.toInt()
    }

    @JvmStatic
    private fun initImageMaxWidthRatio(context: Context): Float {
        val typedValue = TypedValue()
        context.resources.getValue(R.dimen.max_text_image_width_ration, typedValue, true)
        val result = typedValue.float
        DebugUtil.i(TextImageMixLayout.TAG, "initMaxTextWidth result $result")
        return result
    }

    /**
     * 计算图片高度, maxTextWidth 文字的最大宽度， imageMaxWidthRatio 最大的
     */
    @JvmStatic
    fun caculateImageViewWithAndHeight(
        markDataBean: MarkDataBean,
        maxtWithAndRatio: ImageViewMaxtWithAndRatio
    ): ImageViewShowConfig {
        // 最大规则和长图的极限规则
        val imageBitmapDimen = ImageParseResult(markDataBean.pictureWith, markDataBean.pictureHeight)
        val widHeightRatio = imageBitmapDimen.getWithHeightRatio()
        val isLongPicture = imageBitmapDimen.isLongPicture()
        var maxImageViewWidth = maxtWithAndRatio.maxTextWidth
        if (FeatureOption.IS_PAD) {
            maxImageViewWidth = (maxtWithAndRatio.maxTextWidth * BIG_IMAGE_RATIO).toInt()
        }
        val maxImageViewHeight = (maxImageViewWidth / TextImageMixLayout.WIDTH_HEIGHT_STANDARD_RATIO).toInt()
        DebugUtil.i(TAG, "caculateImageViewWithAndHeight imageBitmapDimen $imageBitmapDimen, maxtWithAndRatio $maxtWithAndRatio" +
                ", maxImageViewWidth $maxImageViewWidth, maxImageViewHeight $maxImageViewHeight")
        val result = ImageViewShowConfig()
        //非长图的计算规则
        if (!isLongPicture) {
            val widthOverLimit = imageBitmapDimen.width > maxImageViewWidth
            val heightOverLimit = imageBitmapDimen.height > maxImageViewHeight
            val ratioBigThanStandard = widHeightRatio > TextImageMixLayout.WIDTH_HEIGHT_STANDARD_RATIO
            if (widthOverLimit && heightOverLimit) {
                //宽高都超限了
                if (ratioBigThanStandard) {
                    //宽度相比4：3区域比例更长，宽度会先到达极限
                    result.imageViewWidth = maxImageViewWidth
                    result.imageViewHeight = (maxImageViewWidth / widHeightRatio).toInt()
                } else {
                    //高度相比4：3区域比例更长，高度会先到达极限
                    result.imageViewWidth = (maxImageViewHeight * widHeightRatio).toInt()
                    result.imageViewHeight = maxImageViewHeight
                }
            } else if (!widthOverLimit && heightOverLimit) {
                //高度超限，宽度未超限
                result.imageViewWidth = (maxImageViewHeight * widHeightRatio).toInt()
                result.imageViewHeight = maxImageViewHeight
            } else if (widthOverLimit && !heightOverLimit) {
                //宽度超限，高度为超限
                result.imageViewWidth = maxImageViewWidth
                result.imageViewHeight = (maxImageViewWidth / widHeightRatio).toInt()
            } else {
                //宽高都未超限，将图片宽高设置为控件宽高
                result.imageViewWidth = imageBitmapDimen.width
                result.imageViewHeight = imageBitmapDimen.height
            }
        } else {
            //长图的计算规则
            result.needFitCenter = true
            val isLandScapLongPicture = imageBitmapDimen.isLandScapLongPicture()
            if (isLandScapLongPicture) {
                //横屏长图处理，居中裁切，width取到最大，height为width的1/4处理
                result.imageViewWidth = maxImageViewWidth
                result.imageViewHeight = (maxImageViewWidth / ImageParseResult.LONG_PICTURE_LANDSCAPE_RATIO_LIMIT).toInt()
            } else {
                //竖屏长图处理，居中裁切，height取到最大,width为height的1/3处理
                result.imageViewWidth = (maxImageViewHeight * ImageParseResult.LONG_PICTURE_PORTRAIT_RATIO_LIMIT).toInt()
                result.imageViewHeight = maxImageViewHeight
            }
        }
        DebugUtil.i(TAG, "caculateImageViewWithAndHeight result $result")
        return result
    }
}