/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: LoadingViewControl
 * Description:
 * Version: 1.0
 * Date: 2023/6/20
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/20 1.0 create
 */

package com.soundrecorder.playback

import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.ext.TAG
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil

class LoadingViewControl(private val lifecycle: Lifecycle, private val loadingView: View, private val tabView: View, private val contentView: View) :
    DefaultLifecycleObserver {

    private var mAnimationView: EffectiveAnimationView? = null

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        if (loadingView.isVisible) {
            getAnimationView()?.resumeAnimation()
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        if (loadingView.isVisible) {
            getAnimationView()?.pauseAnimation()
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        cancelAnimation()
        owner.lifecycle.removeObserver(this)
    }

    /**
     * 显示loading，隐藏contentView
     * @param showLoading 是否显示loading
     * @param showTabView 是否显示tab
     */
    fun showLoading(showLoading: Boolean, showTabView: Boolean) {
        DebugUtil.d(TAG, "showLoading:$showLoading, showTabView:$showTabView")
        if (!showLoading) { // 不显示loading
            loadingView.isVisible = false
            contentView.isVisible = true
            tabView.isVisible = showTabView
            return
        } // 显示loading
        tabView.isVisible = showTabView
        loadingView.isVisible = true
        contentView.isVisible = false
        getAnimationView()?.apply {
            clearAnimation()
            if (NightModeUtil.isNightMode(context)) {
                setAnimation(com.soundrecorder.common.R.raw.loading_night)
            } else {
                setAnimation(com.soundrecorder.common.R.raw.loading)
            }
            playAnimation()
        }
        lifecycle.addObserver(this)
    }

    /**
     * 隐藏loading，显示contentView
     * @param showTabView 是否显示标题TAB
     */
    fun hideLoading(showTabView: Boolean) {
        cancelAnimation()
        loadingView.isVisible = false
        contentView.isVisible = true
        tabView.isVisible = showTabView
        lifecycle.removeObserver(this)
    }

    private fun cancelAnimation() {
        if (loadingView.isVisible) {
            getAnimationView()?.apply {
                cancelAnimation()
            }
            mAnimationView = null
        }
    }

    private fun getAnimationView(): EffectiveAnimationView? {
        if (mAnimationView == null) {
            mAnimationView = loadingView.findViewById(com.soundrecorder.common.R.id.loadingView)
        }
        return mAnimationView
    }
}