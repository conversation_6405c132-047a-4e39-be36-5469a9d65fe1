/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute.detector.media

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import oplus.multimedia.soundrecorder.playback.mute.MuteAudioPCMAsyncManager
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MuteDataDetectorMediaTest {

    @Test
    fun cancelTest() {
        val detector = MuteDataDetectorMedia()
        detector.cancel()
        val taskManager = Whitebox.getInternalState<MuteDataDetectorMediaTask>(detector, "mTaskManager")
        Assert.assertTrue(taskManager == null)

        val pcmManager = Whitebox.getInternalState<MuteAudioPCMAsyncManager>(detector, "mPcmManager")
        Assert.assertTrue(pcmManager.isCancelled())
    }

    @Test
    fun releaseTest() {
        val detector = MuteDataDetectorMedia()
        detector.release()
        val taskManager = Whitebox.getInternalState<MuteDataDetectorMediaTask>(detector, "mTaskManager")
        Assert.assertTrue(taskManager == null)
    }
}