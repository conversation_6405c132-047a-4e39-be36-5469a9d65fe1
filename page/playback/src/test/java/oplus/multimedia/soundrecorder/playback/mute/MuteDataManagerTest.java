package oplus.multimedia.soundrecorder.playback.mute;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

import kotlinx.coroutines.CoroutineScope;
import oplus.multimedia.soundrecorder.playback.mute.detector.IMuteDataDetector;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class MuteDataManagerTest {

    @Test
    public void should_returnTrue_when_checkBasicInfoInvalid() throws Exception {
        CoroutineScope coroutineScope = Mockito.spy(CoroutineScope.class);
        MuteDataManager muteDataManager = new MuteDataManager(coroutineScope, 1);
        Whitebox.setInternalState(muteDataManager, "mediaId", 1L, MuteDataManager.class);
        boolean isflag = Whitebox.invokeMethod(muteDataManager, "checkBasicInfoValid");
        Assert.assertFalse(isflag);
        boolean isflag2 = Whitebox.invokeMethod(muteDataManager, "loadMuteDataFromFile");
        Assert.assertFalse(isflag2);
    }

    @Test
    public void should_returnNotnull_when_loadMuteDataFromOrigin() throws Exception {
        CoroutineScope coroutineScope = Mockito.spy(CoroutineScope.class);
        MuteDataManager muteDataManager = new MuteDataManager(coroutineScope, 1);
        Whitebox.setInternalState(muteDataManager, "mediaId", 1l, MuteDataManager.class);
        Whitebox.invokeMethod(muteDataManager, "loadMuteDataFromOrigin");
        IMuteDataDetector muteDataDetector = Whitebox.getInternalState(muteDataManager, "muteDataDetector");
        Assert.assertNotNull(muteDataDetector);
    }
}

