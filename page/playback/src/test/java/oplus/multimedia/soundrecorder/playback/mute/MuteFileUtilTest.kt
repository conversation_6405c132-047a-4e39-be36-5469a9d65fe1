package oplus.multimedia.soundrecorder.playback.mute

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import org.junit.Assert.*

import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog
import java.io.File

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowFeatureOption::class])
class MuteFileUtilTest {

    @Test
    fun deleteTest() {
        val file = File("test.txt")
        if (!file.exists()) {
            file.createNewFile()
        }
        val isDelete = MuteFileUtil.delete(file)
        assertTrue(isDelete)
    }

    @Test
    fun checkOrCreateFileTest() {
        val file = MuteFileUtil.checkOrCreateFile("filename.txt")
        assertNotNull(file)
    }

}