package oplus.multimedia.soundrecorder.playback.mute

import android.content.Context
import android.media.MediaFormat
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Assert.*
import org.junit.Before

import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog
import java.io.File
import java.lang.Exception

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowFeatureOption::class])
class MuteUtilTest {

    companion object {
        private const val MEDIA_ID = 1L
        private const val LAST_MODIFY = 10000L
    }
    private var mContext: Context? = null
    private val mParentPath = "parent"

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        mContext = null
        val file = File(mParentPath)
        deleteFile(file)
    }

    private fun deleteFile(file: File) {
        if (file.isFile) {
            println("\n --------------delete file: ${file.name}")
            file.delete()
        } else {
            val files = file.listFiles()
            files?.forEach {
                deleteFile(it)
            }
        }
    }

    @Test
    fun matchFileTest2() {
        val matchFile = MuteUtil.matchFile("filepath")
        println("\n--------------matchFile: $matchFile")
        assertNull(matchFile)
    }

    @Test
    fun matchFileIdTest() {
        val matchFile = MuteUtil.matchFile(MEDIA_ID)
        println("\n--------------matchFile: $matchFile")
        assertNull(matchFile)
    }

    @Test
    fun isMuteFileInvalid() {
        val isMuteFileInvalid = mContext?.let { MuteUtil.isMuteFileInvalid(it, "test_123") }
        if (isMuteFileInvalid != null) {
            assertTrue(isMuteFileInvalid)
        }
    }

    @Test
    fun getMutePathTest() {
        val mutePath = MuteUtil.getMutePath()
        val isMutePath = mutePath.endsWith(MuteConstants.MUTE_PATH)
        assertTrue(isMutePath)
    }

    @Test
    fun genMuteFileNameTest() {
        val fileName = MuteUtil.genMuteFileName("filepath", MEDIA_ID, LAST_MODIFY)
        val endsWith = fileName.endsWith("_$MEDIA_ID" + "_$LAST_MODIFY")
        assertTrue(endsWith)
    }

    @Test
    fun matchFileTest() {
        val matchFile = MuteUtil.matchFile(MEDIA_ID, LAST_MODIFY)
        println("\n--------------matchFile: $matchFile")
        assertNull(matchFile)
    }

    @Test
    fun getMuteFilesTest() {
        val filepath1 = "$mParentPath/file1.txt"
        val filepath2 = "$mParentPath/file2.txt"
        val filepath3 = "$mParentPath/file3.txt"
        val dir = File(mParentPath)
        println("\n--------------dir: ${dir.name}")
        if (!dir.exists()) {
            dir.mkdir()
        }
        val file1 = File(filepath1)
        val file2 = File(filepath2)
        val file3 = File(filepath3)
        if (!file1.exists()) {
            file1.createNewFile()
        }
        if (!file2.exists()) {
            file2.createNewFile()
        }
        if (!file3.exists()) {
            file3.createNewFile()
        }
        val files = Whitebox.invokeMethod<List<File>>(MuteUtil, "getMuteFiles", mParentPath)
        assertNotNull(files)
        assertEquals(3, files?.size)
        println("\n--------------files: $files")
        deleteAllFiles(dir)
    }


    @Test
    fun getMuteFilesTest2() {
        val filepath1 = "$mParentPath/file1.txt"
        val filepath2 = "$mParentPath/file2.txt"
        val filepath3 = "$mParentPath/file3.txt"
        val dir = File(mParentPath)
        println("\n--------------dir: ${dir.name}")
        if (!dir.exists()) {
            dir.mkdir()
        }
        val file1 = File(filepath1)
        val file2 = File(filepath2)
        val file3 = File(filepath3)
        if (!file1.exists()) {
            file1.createNewFile()
        }
        if (!file2.exists()) {
            file2.createNewFile()
        }
        if (!file3.exists()) {
            file3.createNewFile()
        }
        val files = MuteUtil.getSuffixMap()
        assertNotNull(files)
        assertEquals(0, files.size)
        println("\n--------------files: $files")
        deleteAllFiles(dir)
    }

    private fun deleteAllFiles(root: File) {
        val files = root.listFiles()
        if (files != null) {
            for ( f in files) {
                if (f.isDirectory) {
                    deleteAllFiles(f)
                    try {
                        f.delete()
                    } catch (e: Exception) {
                    }
                } else {
                    if (f.exists()) {
                        deleteAllFiles(f)
                        try {
                            f.delete()
                        } catch (e: Exception) {
                        }
                    }
                }
            }
        }
    }

    @Test
    fun should_equals_when_getInteger() {
        val mediaFormat = MediaFormat()
        val result = MuteUtil.getInteger(mediaFormat, MediaFormat.KEY_CHANNEL_COUNT, 2)
        Assert.assertEquals(2, result)
    }

    @Test
    fun should_equals_when_getString() {
        val mediaFormat = MediaFormat()
        val result = MuteUtil.getString(mediaFormat, MediaFormat.KEY_MIME, "mp3")
        Assert.assertEquals("mp3", result)
    }
}