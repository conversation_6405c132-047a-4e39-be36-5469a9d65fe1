/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute.detector.media

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import oplus.multimedia.soundrecorder.playback.mute.MuteItem
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import org.mockito.Mockito.*

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
class MuteDataDetectorMediaTaskTest {

    @Test
    @Ignore
    fun insertMuteDataTest() {
        val mockMuteData = mutableListOf<MuteItem>()
        val task = mock(MuteDataDetectorMediaTask::class.java)
        Whitebox.setInternalState(task, "mMuteData", mockMuteData)
        `when`(task.insertMuteData(100, 807)).thenCallRealMethod()
        task.insertMuteData(100, 807)
        `when`(task.insertMuteData(0, 716)).thenCallRealMethod()
        task.insertMuteData(0, 716)
        `when`(task.insertMuteData(1024, 1106)).thenCallRealMethod()
        task.insertMuteData(1024, 1106)
        `when`(task.insertMuteData(1312, 1500)).thenCallRealMethod()
        task.insertMuteData(1312, 1500)
        `when`(task.insertMuteData(1312, 1400)).thenCallRealMethod()
        task.insertMuteData(1312, 1400)
        `when`(task.insertMuteData(1839, 2193)).thenCallRealMethod()
        task.insertMuteData(1839, 2193)
        `when`(task.insertMuteData(1839, 2214)).thenCallRealMethod()
        task.insertMuteData(1839, 2214)
        Assert.assertEquals(0, mockMuteData[0].startTime)
    }
}