/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert;


import static org.mockito.ArgumentMatchers.anyLong;

import android.app.Dialog;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.widget.TextView;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.common.share.IShareListener;
import com.soundrecorder.common.utils.ConvertDbUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowDialog;
import org.robolectric.shadows.ShadowLog;
import org.robolectric.shadows.ShadowToast;

import java.io.File;

import com.soundrecorder.playback.PlaybackActivity;
import com.soundrecorder.playback.R;
import com.soundrecorder.playback.newconvert.convert.ConvertManagerImpl;
import com.soundrecorder.playback.shadows.ShadowCOUIMaxHeightScrollView;
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class,
        ShadowOplusUsbEnvironment.class,
        ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class,
        ShadowCOUIMaxHeightScrollView.class,
        ShadowCOUIVersionUtil.class})
public class ExportHelperTest {

    private static final String TEST_CONVERT_FILE_PATH = "test_convert_file_path";
    private static final String TEST_CLIP_DATA = "test_clip_data";

    private Context mContext;
    private ActivityController<PlaybackActivity> mController;
    private PlaybackActivity mActivity;
    private ConvertManagerImpl mConvertManager;
    private TextView mExportMenu;

    private ExportHelper mHelper;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        mController = Robolectric.buildActivity(PlaybackActivity.class);
        mActivity = mController.get();
        mExportMenu = mActivity.findViewById(R.id.layout_convert_export);
        mConvertManager = new ConvertManagerImpl();
        mHelper = new ExportHelper(mConvertManager, null);
    }

    @After
    public void tearDown() {
        mContext = null;
        mController = null;
        mActivity = null;
        mExportMenu = null;
        mConvertManager = null;
        mHelper = null;
    }

    @Test
    public void should_return_true_when_getExportDialogIsShowing() {
        Assert.assertNull(Whitebox.getInternalState(mHelper, "mExportDialog"));
        Whitebox.setInternalState(mHelper, "mActivity", mActivity);
        mHelper.showExportDialog(null);
        //Assert.assertFalse(mHelper.getExportDialogIsShowing());
    }

    @Test
    public void should_show_export_dialog_when_showExportDialog() throws Exception {
        mHelper.showExportDialog(null);
        Dialog latestDialog = ShadowDialog.getLatestDialog();
        Assert.assertNull(latestDialog);

        Whitebox.setInternalState(mHelper, "mActivity", mActivity);
        mHelper.showExportDialog(null);
    }

    @Test
    public void should_show_saving_dialog_when_showSavingDialog() throws Exception {
        Whitebox.invokeMethod(mHelper, "showSavingDialog");
        Dialog latestDialog = ShadowDialog.getLatestDialog();
        Assert.assertNull(latestDialog);

        Whitebox.setInternalState(mHelper, "mActivity", mActivity);
        //Whitebox.invokeMethod(mHelper, "showSavingDialog");
        latestDialog = ShadowDialog.getLatestDialog();
        //Assert.assertNotNull(latestDialog);
    }

    @Test
    public void should_dismiss_saving_dialog_when_dismissSavingDialog() throws Exception {
        Whitebox.setInternalState(mHelper, "mActivity", mActivity);
        //Whitebox.invokeMethod(mHelper, "showSavingDialog");
        Dialog latestDialog  = ShadowDialog.getLatestDialog();
        //Assert.assertNotNull(latestDialog);

        Whitebox.invokeMethod(mHelper, "dismissSavingDialog");
        Assert.assertNull(Whitebox.getInternalState(mHelper, "mLoadingDialog"));
    }

    @Test
    public void should_null_when_release() throws Exception {
        Assert.assertNull(Whitebox.getInternalState(mHelper, "mExportDialog"));
        Whitebox.setInternalState(mHelper, "mActivity", mActivity);
        Assert.assertNull(Whitebox.getInternalState(mHelper, "mExportDialog"));

        mHelper.release();
        Assert.assertNull(Whitebox.getInternalState(mHelper, "mExportDialog"));
        Assert.assertNull(Whitebox.getInternalState(mHelper, "convertManagerImpl"));
    }

    @Test
    public void should_set_clip_data_when_clipboard() throws Exception {
        Whitebox.invokeMethod(mHelper, "clipboard", new Object[]{mContext, TEST_CLIP_DATA});
        ClipboardManager manager = (ClipboardManager) mContext.getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData data = manager.getPrimaryClip();
        Assert.assertNotNull(data);
    }


    @Test
    public void should_return_convert_file_name_when_getConvertFileName() throws Exception {
        ConvertRecord convertRecord = new ConvertRecord();
        convertRecord.setConvertTextfilePath(TEST_CONVERT_FILE_PATH);
        MockedStatic<ConvertDbUtil> mocked = Mockito.mockStatic(ConvertDbUtil.class);
        mocked.when(() -> ConvertDbUtil.selectByRecordId(anyLong())).thenReturn(null);
        Assert.assertNull(Whitebox.invokeMethod(mHelper, "getConvertFileName", mContext));
        mocked.when(() -> ConvertDbUtil.selectByRecordId(anyLong())).thenReturn(convertRecord);
        Assert.assertEquals(TEST_CONVERT_FILE_PATH, Whitebox.invokeMethod(mHelper, "getConvertFileName", mContext));
        mocked.close();
    }

    @Ignore
    public void should_returnNull_when_executeExportFormatDoc() throws Exception {
        IShareListener shareTxtCallback = Mockito.mock(IShareListener.class);
        Whitebox.invokeMethod(mHelper, "executeExportFormatDoc", mActivity, shareTxtCallback);
        Dialog latestDialog = ShadowDialog.getLatestDialog();
        Assert.assertNull(latestDialog);
    }

    @Test
    public void should_returnNull_when_startShareWithTxtActivity() throws Exception {
        Whitebox.invokeMethod(mHelper, "startShareWithTxtActivity", mActivity);
        Intent actual = ShadowApplication.getInstance().getNextStartedActivity();
        Assert.assertNull(actual);
    }

    @Test
    public void should_returnNull_when_genTextFileOld() throws Exception {
        File file = Whitebox.invokeMethod(mHelper, "genTextFileOld", mActivity, 11l);
        Assert.assertNull(file);
    }

    @Test
    public void should_returnNull_when_getConvertFileName() throws Exception {
        String fileName = Whitebox.invokeMethod(mHelper, "getConvertFileName", mContext);
        Assert.assertNull(fileName);
    }

    @Test
    public void should_returnNull_when_getExportConvertTextContent() throws Exception {
        String fileName = Whitebox.invokeMethod(mHelper, "getExportConvertTextContent", 1, false);
        Assert.assertNotNull(fileName);
    }

    @Ignore
    @Test
    public void should_return_flase_when_note_support_img() throws Exception {
        Boolean isNoteSupportImg = Whitebox.invokeMethod(mHelper, "isNoteSupportImg", mActivity);
        Assert.assertFalse(isNoteSupportImg);
    }

    @Test
    public void should_show_when_toast() throws Exception {
        Whitebox.invokeMethod(mHelper, "showFailTip");
        Assert.assertNotNull(ShadowToast.getLatestToast());
        Assert.assertEquals(mContext.getResources().getString(com.soundrecorder.common.R.string.save_failed_tip), ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void should_show_note_tip_dialog_when_showNoteTipDialog() throws Exception {
        Whitebox.invokeMethod(mHelper, "showNoteTipDialog", mActivity);
        Dialog latestDialog = ShadowDialog.getLatestDialog();
        Assert.assertNotNull(latestDialog);
    }
}
