package com.soundrecorder.setting.opensource

import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.TextView
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.setting.R
import java.io.BufferedReader
import java.io.InputStreamReader

class OpenSourceActivity : BaseActivity() {

    companion object {
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "OpenSource"
        private const val TAG = "RecordLicenceActivity"
        private const val TITLE = "OPEN SOURCE SOFTWARE NOTICE"
        private const val GBK = "gbk"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_open_source)

        initView()
    }

    private fun initView() {
        val toolbar: COUIToolbar = findViewById(R.id.toolbar)
        toolbar.title = getString(com.soundrecorder.common.R.string.record_license)
        setSupportActionBar(toolbar)
        supportActionBar?.setHomeButtonEnabled(true)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        findViewById<TextView>(R.id.tv_title).setText(TITLE)
        findViewById<TextView>(R.id.tv_licence).setText(getStatement())
        initiateWindowInsets()
    }

    private fun initiateWindowInsets() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        val root = findViewById<View>(R.id.root_layout)
        val callback = object : RootViewPersistentInsetsCallback() {
            override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                super.onApplyInsets(v, insets)
                DebugUtil.i(TAG, "onApplyInsets")
                TaskBarUtil.setNavigationColorOnSupportTaskBar(
                    navigationHeight = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom,
                    activity = this@OpenSourceActivity,
                    defaultNoTaskBarColor = navigationBarColor()
                )
            }
        }
        ViewCompat.setOnApplyWindowInsetsListener(root, callback)
    }

    private fun getStatement(): String {
        val sb = StringBuffer("")
        resources.openRawResource(R.raw.notice).use { inputStream ->
            InputStreamReader(inputStream, GBK).use {
                val reader = BufferedReader(it)
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    sb.append(line)
                    sb.append("\n")
                }
            }
        }
        return sb.toString()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        DebugUtil.e(TAG, "onOptionsItemSelected item:" + item.itemId)
        if (ClickUtils.isFastDoubleClick()) {
            DebugUtil.e(TAG, "onOplusOptionsMenuItemSelected() isFastDoubleClick return")
            return false
        }
        when (item.itemId) {
            android.R.id.home -> {
                setResult(RESULT_CANCELED)
                finish()
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun navigationBarColor(): Int {
        return com.support.appcompat.R.color.coui_color_background
    }
}