package com.soundrecorder.setting.widget

import android.content.Context
import android.util.AttributeSet
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.preference.COUIPreference
import com.soundrecorder.setting.R

class UnClickablePreference @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = androidx.preference.R.attr.preferenceStyle,
    defStyleRes: Int = 0
) : COUIPreference(context, attrs, defStyleAttr, defStyleRes) {

    override fun onBindViewHolder(holder: PreferenceViewHolder?) {
        super.onBindViewHolder(holder)
        holder?.let {
            (it.itemView as COUICardListSelectedItemLayout).setBackgroundAnimationEnabled(false)
        }
    }
}