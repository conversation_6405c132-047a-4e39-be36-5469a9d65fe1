/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BootRegUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting

import android.app.Activity
import android.content.Intent
import androidx.appcompat.app.AlertDialog
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OplusCompactUtil
import com.soundrecorder.common.constant.OplusCompactConstant
import com.soundrecorder.common.utils.EnableAppUtil

@Suppress("TooGenericExceptionCaught")
object BootRegUtil {

    private const val TAG = "BootRegUtil"

    @JvmStatic
    fun launchBootRegPrivacy(activity: Activity?, disableDialogFun: ((dialog: AlertDialog) -> Unit)) {
        val act = activity ?: return
        /*oppo包名*/
        val defaultResult = launchBootRegPrivacyWithCheckResult(act, OplusCompactConstant.PACKAGE_BOOTREG_BEFORE, disableDialogFun)
        if (defaultResult == EnableAppUtil.APP_NOT_INSTALLED) {
            /*一加包名*/
            launchBootRegPrivacyWithCheckResult(act, OplusCompactConstant.PACKAGE_BOOTREG_ONEPLUS, disableDialogFun)
        }
    }

    @JvmStatic
    private fun launchBootRegPrivacyWithCheckResult(
        act: Activity,
        packageName: String,
        disableDialogCallback: ((dialog: AlertDialog) -> Unit)? = null
    ): Int {
        val result = EnableAppUtil.isAppInstallEnabled(act, packageName)
        when (result) {
            EnableAppUtil.APP_IS_ENABLE -> goToPrivacyPage(act, packageName)
            EnableAppUtil.APP_IS_DISABLED -> {
               val dialog = EnableAppUtil.showEnableDialog(
                    act,
                    packageName,
                    com.soundrecorder.common.R.string.enable_request_title,
                    com.soundrecorder.common.R.string.boot_wizard_disable_message_v2
                )
                disableDialogCallback?.invoke(dialog)
            }
            EnableAppUtil.APP_NOT_INSTALLED -> DebugUtil.w(TAG, "launchBootRegPrivacyWithCheckResult $packageName not install")
        }
        return result
    }

    @JvmStatic
    private fun goToPrivacyPage(activity: Activity, packageName: String) {
        val intent = Intent()
        intent.setPackage(packageName)
        when (packageName) {
            OplusCompactConstant.PACKAGE_BOOTREG_BEFORE -> {
                OplusCompactUtil.getActionForIntent(
                    intent, OplusCompactConstant.STATEMENT_ACTION_BEFOR, OplusCompactConstant.STATEMENT_ACTION_AFTER)
                intent.putExtra(
                    OplusCompactConstant.FLAG_STATEMENT_INTENT, OplusCompactConstant.FLAG_STATEMENT_INTENT_VALUE)
            }

            OplusCompactConstant.PACKAGE_BOOTREG_ONEPLUS -> {
                intent.action = OplusCompactConstant.STATEMENT_ACTION_ONEPLUS
                intent.putExtra(
                    OplusCompactConstant.FLAG_STATEMENT_INTENT_ONEPLUS, OplusCompactConstant.FLAG_STATEMENT_INTENT_VALUE_ONEPLUS)
            }
        }
        runCatching {
            activity.startActivity(intent)
        }.onFailure {
            DebugUtil.e(TAG, "goToPrivacyPage error $it")
        }
    }
}