/************************************************************
 * Copyright 2000-2019 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : SettingRecordBehavior.java
 * Version Number: 1.0
 * Description   : Impliment SettingRecordBehavior
 * Author        : chen_lipeng
 * Date          : 2019-09-11
 * History       :(ID,  2019-09-11, chen_lipeng, Description)
 */
package com.soundrecorder.setting.opensource

import android.content.Context
import android.os.Parcel
import android.os.Parcelable
import android.os.Parcelable.ClassLoaderCreator
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.ViewCompat
import androidx.core.view.doOnNextLayout
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import androidx.customview.view.AbsSavedState
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.setting.R
import com.google.android.material.appbar.AppBarLayout
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption

class SettingRecordBehavior(
    context: Context,
    attrs: AttributeSet?
) : AppBarLayout.Behavior(context, attrs), View.OnScrollChangeListener {
    private val tag by lazy { javaClass.simpleName }
    private var dividerView: View? = null
    private var targetView: View? = null
    var targetViewId = -1
    private val location = IntArray(2)
    private val dividerAlphaChangeOffset: Int
        get() {
            return if (FeatureOption.IS_PAD) {
                BaseApplication.getAppContext().resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp15)
            } else {
                BaseApplication.getAppContext().resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp10)
            }
        }
    private val dividerWidthChangeOffset: Int
        get() {
            return if (FeatureOption.IS_PAD) {
                BaseApplication.getAppContext().resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp30)
            } else {
                BaseApplication.getAppContext().resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp25)
            }
        }

    private val maxMargin: Int
        get() = BaseApplication.getAppContext().resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.common_margin)

    override fun onSaveInstanceState(parent: CoordinatorLayout, abl: AppBarLayout): Parcelable {
        val savedState = super.onSaveInstanceState(parent, abl) ?: View.BaseSavedState.EMPTY_STATE
        return SavedState(savedState, this)
    }

    override fun onRestoreInstanceState(
        parent: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        state: Parcelable
    ) {
        val savedState = state as? com.soundrecorder.setting.opensource.SavedState
        if (savedState != null) {
            targetView = parent.findViewById(savedState.targetViewId)
            targetViewId = savedState.targetViewId
            dividerView = parent.findViewById(R.id.divider_line)
            targetView?.setOnScrollChangeListener(this)
            targetView?.doOnNextLayout {
                onListScroll()
            }
        }
        super.onRestoreInstanceState(parent, appBarLayout, state)
    }

    override fun onStartNestedScroll(
        coordinatorLayout: CoordinatorLayout,
        child: AppBarLayout,
        directTargetChild: View,
        target: View,
        axes: Int,
        type: Int
    ): Boolean {
        val started = (axes and ViewCompat.SCROLL_AXIS_VERTICAL) != 0
        DebugUtil.d(
            tag,
            "directTargetChild = $directTargetChild , target = $target , started = $started"
        )
        if (started) {
            targetView = target
            targetViewId = target.id
            dividerView = child.findViewById(R.id.divider_line)
            targetView?.setOnScrollChangeListener(this)
        }
        return false
    }

    override fun onScrollChange(
        v: View?,
        scrollX: Int,
        scrollY: Int,
        oldScrollX: Int,
        oldScrollY: Int
    ) {
        onListScroll()
    }

    private fun onListScroll() {
        /**
         * divider 分割线VIew
         */
        val divider = dividerView ?: return

        /**
         * targetView 滑动的嵌套View，如RecyclerView
         */
        val targetView = targetView as? ViewGroup
        if (targetView == null) {
            divider.alpha = 0f
            return
        }
        if (targetView.childCount <= 0) {
            divider.alpha = 0f
            return
        }

        /**
         * firstChildView指targetView的子View
         * 如果targetView为RecyclerView，firstChildView指第一条数据对应的View
         * 反之firstChildView为targetView的第一个显示的子View
         */
        var firstChildView: View? = null
        if (targetView is RecyclerView) {
            val viewHolder = targetView.findViewHolderForLayoutPosition(0)
            if (viewHolder != null && viewHolder.bindingAdapterPosition == 0) {
                firstChildView = viewHolder.itemView
            }
        } else {
            if (targetView.childCount > 0) {
                for (i in 0 until targetView.childCount) {
                    if (targetView.getChildAt(i).visibility == View.VISIBLE) {
                        firstChildView = targetView.getChildAt(i)
                        break
                    }
                }
            }
        }

        val offset = if (firstChildView == null) {
            /**
             * 当firstChildView为null，说明firstChildView滑出了targetView的显示区域，offset直接赋值为dividerWidthChangeOffset
             */
            dividerWidthChangeOffset
        } else {
            /**
             * 当firstChildView不为null，通过targetView相对屏幕的Y减去firstChildView相对屏幕的Y，计算offset值
             */
            firstChildView.getLocationOnScreen(location)
            val childY = location[1] - firstChildView.marginTop
            targetView.getLocationOnScreen(location)
            val targetViewY = location[1] + targetView.paddingTop
            targetViewY - childY
        }

        /**
         * divider的alpha动效
         */
        val alphaOffset = if (offset <= dividerAlphaChangeOffset) {
            offset
        } else {
            dividerAlphaChangeOffset
        }
        divider.alpha = alphaOffset * 1f / dividerAlphaChangeOffset

        /**
         * divider的margin动效
         */
        val marginOffset = if (offset <= dividerWidthChangeOffset) {
            offset
        } else {
            dividerWidthChangeOffset
        }
        divider.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            val range = marginOffset * 1f / dividerWidthChangeOffset
            val margin = (maxMargin * (1 - range)).toInt()
            leftMargin = margin
            rightMargin = margin
        }
    }
}

class SavedState : AbsSavedState {
    val targetViewId: Int

    @JvmOverloads
    constructor(source: Parcel, loader: ClassLoader? = null) : super(source, loader) {
        targetViewId = source.readInt()
    }

    constructor(superState: Parcelable?, behavior: SettingRecordBehavior) : super(
        superState ?: EMPTY_STATE
    ) {
        targetViewId = behavior.targetViewId
    }

    override fun writeToParcel(out: Parcel, flags: Int) {
        super.writeToParcel(out, flags)
        out.writeInt(targetViewId)
    }

    companion object {
        @JvmField
        val CREATOR: Parcelable.Creator<SavedState> = object : ClassLoaderCreator<SavedState> {
            override fun createFromParcel(`in`: Parcel, loader: ClassLoader): SavedState {
                return SavedState(`in`, loader)
            }

            override fun createFromParcel(`in`: Parcel): SavedState {
                return SavedState(`in`, null)
            }

            override fun newArray(size: Int): Array<SavedState> {
                return newArray(size)
            }
        }
    }
}