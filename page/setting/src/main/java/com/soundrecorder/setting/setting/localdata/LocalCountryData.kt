/**
 * Copyright (C), 2024-2028 OPLUS Mobile Comm Corp., Ltd.
 * File: LocalCountryData
 * Description:
 * Version: 1.0
 * Date: 2024/5/21
 * Author: W9071341(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/5/28 1.0 create
 */
package com.soundrecorder.setting.setting.localdata

import android.content.Context
import android.text.TextUtils
import com.soundrecorder.base.utils.DebugUtil

object LocalCountryData {
    const val TAG = "SetRecorder"
    var json = ""
    fun getJson(context: Context): String {
        if (TextUtils.isEmpty(json)) {
            try {
                json = readJsonFromAsset(context, "country_code.json")
            } catch (e: java.lang.Exception) {
                DebugUtil.e(TAG, "read asset fail")
            }
        }
        return json
    }

    /**
     * 读取asset文件
     */
    private fun readJsonFromAsset(context: Context, fileName: String): String {
        context.assets.open(fileName).bufferedReader().use {
            return it.readText()
        }
    }
}