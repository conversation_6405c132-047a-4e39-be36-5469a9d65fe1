/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordAboutActivity
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting.about

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.poplist.COUIPopupWindow
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.utils.XORUtil
import com.soundrecorder.common.flexible.FollowHandPanelUtils
import com.soundrecorder.setting.BuildConfig
import com.soundrecorder.setting.R
import com.soundrecorder.setting.databinding.ActivityRecordAboutBinding

class RecordAboutActivity : BaseActivity() {

    companion object {
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "About"
        private const val TAG = "RecordAboutActivity"
        private const val XOR_KEY = 8
    }

    private var viewBinding: ActivityRecordAboutBinding? = null
    private var popupWindow: COUIPopupWindow? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewBinding = ActivityRecordAboutBinding.inflate(layoutInflater).apply {
            setContentView(this.root)
        }
        supportFragmentManager.beginTransaction().replace(R.id.root_layout, RecordAboutFragment()).commit()
        FollowHandPanelUtils.checkActivityClickOutOfBounds(this, findViewById(R.id.root_layout))

        if (!BaseUtil.isEXP()) {
            viewBinding?.tvIcpInfo?.let { tvIcp ->
                tvIcp.isVisible = true
                tvIcp.setOnClickListener {
                    jumpToICPPage()
                }
                val drawableRect = Rect()
                tvIcp.setOnLongClickListener {
                    if (popupWindow == null) {
                        initPopupWindow(drawableRect)
                    }
                    showPopupWindow(drawableRect, tvIcp)
                    true
                }
            }
        }
    }

    private fun jumpToICPPage() {
        runCatching {
            val targetUrl = XORUtil.enOrDecrypt(BuildConfig.icpUrl, XOR_KEY)
            val uri = Uri.parse(targetUrl)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        }.onFailure {
            DebugUtil.e("TAG", "jumpToICPPage error ${it.message}")
        }
    }

    fun onApplyInsets(navigationHeight: Int) {
        val tvIcp = viewBinding?.tvIcpInfo ?: return
        if (tvIcp.isVisible) {
            tvIcp.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                bottomMargin = navigationHeight + resources.getDimensionPixelOffset(R.dimen.setting_icp_info_margin_bottom)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        popupWindow?.dismiss()
        popupWindow = null
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        DebugUtil.e(TAG, "onOptionsItemSelected item:" + item.itemId)
        if (ClickUtils.isFastDoubleClick()) {
            DebugUtil.e(TAG, "onOplusOptionsMenuItemSelected() isFastDoubleClick return")
            return false
        }
        when (item.itemId) {
            android.R.id.home -> {
                setResult(RESULT_CANCELED)
                finish()
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun initPopupWindow(drawableRect: Rect) {
        val context = this
        popupWindow = COUIPopupWindow(context).apply {
            contentView = LayoutInflater.from(context).inflate(
                    com.support.component.R.layout.coui_component_popup_window_layout,
                    null, false
            )
            ResourcesCompat.getDrawable(context.resources, com.support.poplist.R.drawable.coui_popup_window_bg, null)
                    ?.apply {
                        getPadding(drawableRect)
                        setBackgroundDrawable(this)
                    }
            contentView.findViewById<TextView>(com.support.component.R.id.popup_window_copy_body).apply {
                text = getString(com.soundrecorder.common.R.string.text_copy)
                setOnClickListener {
                    setTextToClipboard(viewBinding?.tvIcpInfo?.text ?: "")
                    dismiss()
                }
            }
            setDismissTouchOutside(true)
        }
    }

    @SuppressLint("ClipboardManagerDetector")
    private fun setTextToClipboard(text: CharSequence) {
        runCatching {
            val cm = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            cm.setPrimaryClip(ClipData.newPlainText(null, text))
            ToastManager.showShortToast(
                BaseApplication.getAppContext(),
                getString(com.soundrecorder.common.R.string.copied)
            )
        }.onFailure {
            DebugUtil.w(TAG, "setTextToClipboard error ${it.message}")
        }
    }

    /**
     * 弹出[popupWindow]
     */
    private fun showPopupWindow(drawableRect: Rect, view: View) {
        val offsetX =
                (drawableRect.left + drawableRect.right + resources.getDimensionPixelOffset(
                        com.support.component.R.dimen.coui_component_copy_window_width
                ) - view.measuredWidth) / 2
        val offsetY = view.measuredHeight + resources.getDimensionPixelOffset(
                com.support.component.R.dimen.coui_component_copy_window_height
        ) + resources.getDimensionPixelOffset(
                com.support.component.R.dimen.coui_component_copy_window_margin_bottom
        ) + drawableRect.top
        val xOff = if (BaseApplication.sIsRTLanguage) {
                    -view.measuredWidth - offsetX
                } else {
                    -offsetX
                }
        popupWindow?.showAsDropDown(view, xOff, -offsetY)
    }
}