package com.soundrecorder.setting.setting.dialog

import android.content.Context
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.adapter.ChoiceListAdapter
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.utils.ViewUtils

/**
 * 外销选择录音音频格式弹窗
 */
class SelectRecordAudioFormatDialog(val context: Context) {
    companion object {
        const val ITEM_POS_MP3 = 0
        const val ITEM_POS_AAC = 1
        const val ITEM_POS_WAV = 2

        const val AAC_SHOW_STR = "AAC"
        const val WAV_SHOW_STR = "WAV"
        const val MP3_SHOW_STR = "MP3"
    }

    private var mDialog: AlertDialog? = null
    var mDialogItemListener: DialogChildClickListener? = null


    fun showDialog(selectFormat: Int) {
        var mode = arrayOf(MP3_SHOW_STR, AAC_SHOW_STR)
        var description =
            arrayOf(
                context.getString(com.soundrecorder.common.R.string.mp3_audio_summary),
                context.getString(com.soundrecorder.common.R.string.aac_audio_summary)
            )
        if (isSupportWav()) {
            mode = mode.plus(WAV_SHOW_STR)
            description = description.plus(context.getString(com.soundrecorder.common.R.string.wav_audio_summary))
        }
        val selectPos = getItemPos(selectFormat)
        val checkboxStates = booleanArrayOf(false, false, false)
        val disableStatus = booleanArrayOf(false, false, false)
        checkboxStates[selectPos] = true
        val singleChoiceListAdapter = ChoiceListAdapter(
            context,
            com.support.dialog.R.layout.coui_select_dialog_singlechoice,
            mode,
            description, checkboxStates, disableStatus, false
        )
        mDialog = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_BottomAssignment)
            .setBlurBackgroundDrawable(true)
            .setTitle(context.getString(com.soundrecorder.common.R.string.record_audio_format))
            .setAdapter(singleChoiceListAdapter) { _, which ->
                mDialogItemListener?.click(calAudioFormatByPos(which))
                release()
            }
            .setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
            .show()
        ViewUtils.updateWindowLayoutParams(mDialog?.window)
    }

    fun release() {
        mDialog?.dismiss()
        mDialogItemListener = null
        mDialog = null
    }

    private fun getItemPos(selectFormat: Int): Int = when (selectFormat) {
        RecorderConstant.RECORDER_AUDIO_FORMAT_MP3 -> ITEM_POS_MP3
        RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS -> ITEM_POS_AAC
        RecorderConstant.RECORDER_AUDIO_FORMAT_WAV -> ITEM_POS_WAV
        else -> ITEM_POS_MP3
    }

    private fun calAudioFormatByPos(pos: Int): Int {
        return when (pos) {
            ITEM_POS_MP3 -> RecorderConstant.RECORDER_AUDIO_FORMAT_MP3
            ITEM_POS_AAC -> RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS
            ITEM_POS_WAV -> RecorderConstant.RECORDER_AUDIO_FORMAT_WAV
            else -> RecorderConstant.RECORDER_AUDIO_FORMAT_MP3
        }
    }

    /**
     * os12 媒体库bug，导致wav录制失败
     * 再os12上屏蔽wav录制功能
     */
    private fun isSupportWav(): Boolean {
        return !OS12FeatureUtil.isColorOs12()
    }

    interface DialogChildClickListener {
        fun click(audioFormat: Int)
    }
}