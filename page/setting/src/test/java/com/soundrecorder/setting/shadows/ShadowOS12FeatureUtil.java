/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ShadowOS12FeatureUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
// OPLUS Java File Skip Rule:RegexpOnFilename
package com.soundrecorder.setting.shadows;

import com.soundrecorder.base.utils.OS12FeatureUtil;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(OS12FeatureUtil.class)
public class ShadowOS12FeatureUtil {

    @Implementation
    public static boolean isSuperSoundRecorderEpicEffective() {
        return true;
    }

    @Implementation
    public static boolean readFeatureByOplusFeature() {
        return true;
    }

    @Implementation
    public static boolean isFindX4AndNotConfidential() {
        return true;
    }

    @Implementation
    public static boolean isColorOs12() {
        return false;
    }
}
