/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SettingApiTest
 Description:
 Version: 1.0
 Date: 2023/05/25 1.0
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2023/05/25 1.0 create
 */

package com.soundrecorder.setting

import android.content.Context
import android.os.Build
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.setting.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowFeatureOption::class]
)
class SettingApiTest {

    private var context: Context? = null
    private var mActivity: AppCompatActivity? = null
    private var mFragment: Fragment? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mActivity = Robolectric.buildActivity(AppCompatActivity::class.java).get()
        mFragment = Mockito.mock(Fragment::class.java)
    }

    @After
    fun tearDown() {
        context = null
        mActivity = null
    }

    @Test
    fun should_contains_when_launchForResult() {
        mFragment?.let {
            Mockito.`when`(it.context).thenReturn(context)
            SettingApi.launchForResult(it, 1000)
        }
    }

    @Test
    fun should_contains_when_launchBootRegPrivacy() {
        SettingApi.launchBootRegPrivacy(mActivity) {
        // do nothing
        }
    }

    @Test
    fun should_contains_when_launchRecordPrivacy() {
        SettingApi.launchRecordPrivacy(mActivity, 1)
    }

    @Test
    fun should_contains_when_getPageNameInSetting() {
        val list = SettingApi.getPageNameInSetting()
        Assert.assertTrue(list.size == 4)
    }
}