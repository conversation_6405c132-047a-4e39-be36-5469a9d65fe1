/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordModeDialog
 * Description:
 * Version: 1.0
 * Date: 2023/6/29
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/29 1.0 create
 */

package com.soundrecorder.setting.setting.dialog

import android.content.Context
import android.os.Build
import androidx.appcompat.app.AlertDialog
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.setting.setting.SettingRecorderActivity
import com.soundrecorder.setting.shadows.ShadowFeatureOption
import com.soundrecorder.setting.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.RuntimeEnvironment
import org.robolectric.Shadows
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import com.soundrecorder.common.R

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class RecordModeDialogTest {
    private var mContext: Context? = null
    private var mController: ActivityController<SettingRecorderActivity>? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mController = Robolectric.buildActivity(SettingRecorderActivity::class.java)
    }

    @After
    fun tearDown() {
        mContext = null
        mController = null
    }

    @Test
    fun should_when_getModeName() {
        var name = RecordModeDialog.getModeName(0)
        Assert.assertEquals(mContext!!.getString(R.string.standard_mode), name)

        name = RecordModeDialog.getModeName(1)
        Assert.assertEquals(mContext!!.getString(R.string.conference_mode), name)

        name = RecordModeDialog.getModeName(2)
        Assert.assertEquals(mContext!!.getString(R.string.interview_mode), name)

        name = RecordModeDialog.getModeName(-1)
        Assert.assertEquals(mContext!!.getString(R.string.standard_mode), name)
    }

    @Test
    fun should_when_isNeedShowRecordModeRedDot() {
        PrefUtil.putBoolean(
            BaseApplication.getAppContext(),
            "record_mode_redDot_visible",
            false
        )
        Assert.assertTrue(RecordModeDialog.isNeedShowRecordModeRedDot())
    }

    @Test
    fun should_when_setRecordModeRedDotShowed() {
        RecordModeDialog.setRecordModeRedDotShowed()
        Assert.assertFalse(RecordModeDialog.isNeedShowRecordModeRedDot())
    }

    @Test
    fun should_notNull_when_showDialog() {
        val activity = mController!!.create().get()
        val modeHelper = RecordModeDialog(activity!!)
        modeHelper.showDialog(0)
        val realDialog = Shadows.shadowOf(RuntimeEnvironment.application).latestDialog
        Assert.assertNotNull(realDialog)
    }

    @Test
    fun should_null_when_release() {
        val activity = mController!!.create().get()
        val modeHelper = RecordModeDialog(activity!!)
        modeHelper.showDialog(0)
        var dialog = Whitebox.getInternalState<AlertDialog>(modeHelper, "mDialog")
        Assert.assertNotNull(dialog)

        modeHelper.release()
        dialog = Whitebox.getInternalState(modeHelper, "mDialog")
        Assert.assertNull(dialog)
    }
}