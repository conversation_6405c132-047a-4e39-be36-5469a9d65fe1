/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordApiTest
 * Description:
 * Version: 1.0
 * Date: 2023/12/22
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/22 1.0 create
 */

package com.soundrecorder.record

import android.content.Context
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.recorderservice.manager.AudioModeChangeManager
import oplus.multimedia.soundrecorder.BreenoStartRecordUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class RecordApiTest {
    private var context: Context? = null
    private var mockBreenoStartRecordUtil: MockedStatic<BreenoStartRecordUtil>? = null

    @Before
    fun setUp() {
        context = BaseApplication.getAppContext()
        mockBreenoStartRecordUtil = Mockito.mockStatic(BreenoStartRecordUtil::class.java)
    }

    @After
    fun tearDown() {
        context = null
        mockBreenoStartRecordUtil?.close()
        mockBreenoStartRecordUtil = null
    }

    @Test
    fun should_notNull_when_createRecorderIntent() {
        val context = context ?: return
        val intent = RecordApi.createRecorderIntent(context, true)
        Assert.assertNotNull(intent)
    }

    @Test
    fun should_correct_when_canStartRecord() {
        val staticAudioManager = Mockito.mockStatic(AudioModeChangeManager::class.java)
        staticAudioManager.`when`<Boolean> { AudioModeChangeManager.checkModeCanRecord() }
            .thenReturn(false, true)

        Assert.assertFalse(RecordApi.canStartRecord(true, null))

        Assert.assertTrue(RecordApi.canStartRecord(false, null))

        staticAudioManager.close()
    }


    @Test
    fun should_correct_when_isRecorderActivity() {
        val context = context ?: return
        Assert.assertFalse(RecordApi.isRecorderActivity(context))
    }

    @Test
    fun should_correct_when_getRecorderActivityClass() {
        val result = RecordApi.getRecorderActivityClass()
        Assert.assertNotNull(result)
        Assert.assertTrue(result.name.contains("RecorderActivity"))
    }

    @Test
    fun should_correct_when_sendStartForegroundServiceBroadCast() {
        RecordApi.sendStartForegroundServiceBroadCast()
        mockBreenoStartRecordUtil?.verify { BreenoStartRecordUtil.sendStartForegroundServiceBroadCast() }
    }

    @Test
    fun should_correct_when_sendBrowseFrontToRecordBroadCast() {
        RecordApi.sendBrowseFrontToRecordBroadCast()
        mockBreenoStartRecordUtil?.verify { BreenoStartRecordUtil.sendBrowseFrontToRecordBroadCast() }
    }

    @Test
    fun should_correct_when_sendStartOtherPageFailedBroadCast() {
        RecordApi.sendStartOtherPageFailedBroadCast()
        mockBreenoStartRecordUtil?.verify { BreenoStartRecordUtil.sendStartOtherPageFailedBroadCast() }
    }

    @Test
    fun should_correct_when_doCheckExceptionWhenStartRecordFromBreno() {
        mockBreenoStartRecordUtil?.`when`<Boolean> { BreenoStartRecordUtil.doCheckExceptionWhenStartRecordFromBreeno() }
            ?.thenReturn(true, false)
        Assert.assertEquals(true, RecordApi.doCheckExceptionWhenStartRecordFromBreno())
        Assert.assertEquals(false, RecordApi.doCheckExceptionWhenStartRecordFromBreno())
    }

    @Test
    fun should_correct_when_getShowReadImagePermissionTips() {
        RecordImagesPermissionTips.mShowReadImagePermissionTips = true
        Assert.assertTrue(RecordApi.getShowReadImagePermissionTips())

        RecordImagesPermissionTips.mShowReadImagePermissionTips = false
        Assert.assertFalse(RecordApi.getShowReadImagePermissionTips())
    }

    @Test
    fun should_correct_when_setShowReadImagePermissionTips() {
        RecordApi.setShowReadImagePermissionTips(true)
        Assert.assertTrue(RecordImagesPermissionTips.mShowReadImagePermissionTips)

        RecordApi.setShowReadImagePermissionTips(false)
        Assert.assertFalse(RecordImagesPermissionTips.mShowReadImagePermissionTips)
    }
}