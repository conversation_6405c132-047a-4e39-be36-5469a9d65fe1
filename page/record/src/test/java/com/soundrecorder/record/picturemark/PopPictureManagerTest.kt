/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PopPictureManagerTest
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark

import android.content.ContentValues
import android.content.Context
import android.os.Build
import android.provider.MediaStore
import androidx.annotation.WorkerThread
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.record.shadows.ShadowOplusUsbEnvironment
import kotlinx.coroutines.*
import kotlinx.coroutines.Dispatchers.IO
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config
import org.robolectric.annotation.LooperMode

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
@LooperMode(LooperMode.Mode.PAUSED)
class PopPictureManagerTest {

    private val CSHOT_DIR = "Cshot\\Cshotshot"
    private var context: Context? = null
    private var mainScope: CoroutineScope? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mainScope = MainScope()
    }

    @After
    fun tearDown() {
        mainScope?.cancel()
        mainScope = null
        context = null
    }

    @Test
    fun check_isEmpty_when_query_do_not_camera_take_photo() {
        mainScope?.launch(IO) {
            PopTimeSliceManager.startTimeSlice(1000L)
            delay(1000)
            PopTimeSliceManager.endTimeSlice(2000L)
            assertTrue(PopPictureManager.query(PopTimeSliceManager.getTimeSlices(), 100).isEmpty())
            PopTimeSliceManager.clear()
        }
    }

    @WorkerThread
    private fun takePhoto() {
        val context = this.context ?: return
        val cv = ContentValues()
        cv.put(
            MediaStore.Images.Media.DATA,
            "emulated\\0\\DCIM\\Camera\\IMG${System.currentTimeMillis()}.jpg"
        )
        cv.put(MediaStore.Images.Media.DATE_TAKEN, System.currentTimeMillis())
        cv.put(MediaStore.Images.Media.OWNER_PACKAGE_NAME, "com.oplus.camera")
        cv.put(MediaStore.Images.Media.RELATIVE_PATH, "DCIM\\Camera\\")
        context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, cv)
    }

    @WorkerThread
    private fun takeCShotPhoto() {
        val context = this.context ?: return
        val cv = ContentValues()
        cv.put(
            MediaStore.Images.Media.DATA,
            "emulated\\0\\DCIM\\Camera\\${CSHOT_DIR}\\IMG${System.currentTimeMillis()}.jpg"
        )
        cv.put(MediaStore.Images.Media.DATE_TAKEN, System.currentTimeMillis())
        cv.put(MediaStore.Images.Media.OWNER_PACKAGE_NAME, "com.oplus.camera")
        cv.put(MediaStore.Images.Media.RELATIVE_PATH, "DCIM\\Camera\\$CSHOT_DIR\\")
        context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, cv)
    }

    @Test
    fun check_isNotEmpty_query_when_takePhoto() {
        mainScope?.launch(IO) {
            PopTimeSliceManager.startTimeSlice(1000L)
            takePhoto()
            delay(300)
            takePhoto()
            delay(300)
            takePhoto()
            delay(400)
            takePhoto()
            PopTimeSliceManager.endTimeSlice(2000L)
            assertTrue(
                PopPictureManager.query(PopTimeSliceManager.getTimeSlices(), 100L).isNotEmpty()
            )
            PopTimeSliceManager.clear()
        }
    }

    @Test
    fun should_filter_cShot_photo_when_query() {
        mainScope?.launch(IO) {
            PopTimeSliceManager.startTimeSlice(1000L)
            takePhoto()
            delay(300)
            takePhoto()
            delay(300)
            takeCShotPhoto()
            takeCShotPhoto()
            takeCShotPhoto()
            PopTimeSliceManager.endTimeSlice(2000L)
            val popPictureList = PopPictureManager.query(PopTimeSliceManager.getTimeSlices(), 100L)
            assertTrue(popPictureList.isNotEmpty())
            assertEquals(3, popPictureList.size)
            PopTimeSliceManager.clear()
        }
    }

    @Test
    fun check_onBindRecorderActivityOnCreate(){
        PopTimeSliceManager.clear()
        val mok = Mockito.mockStatic(RecorderViewModelAction::class.java)
        mok.`when`<Long> { RecorderViewModelAction.getAmplitudeCurrentTime() }.thenReturn(100L)
        PopTimeSliceManager.onRecordStatusChange(RecorderViewModelAction.RECORDING)
        mok.`when`<Long> { RecorderViewModelAction.getAmplitudeCurrentTime() }.thenReturn(100L)
        PopTimeSliceManager.onBindRecorderActivityOnCreate(RecorderViewModelAction.RECORDING)
        assertTrue(PopTimeSliceManager.getTimeSlices().isNotEmpty())
        mok.reset()
        mok.close()
    }
}