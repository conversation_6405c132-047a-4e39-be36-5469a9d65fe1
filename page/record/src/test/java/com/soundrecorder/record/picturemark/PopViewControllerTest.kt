/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PopViewControllerTest
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark

import android.content.ContentUris
import android.content.ContentValues
import android.os.Build
import android.provider.MediaStore
import androidx.annotation.WorkerThread
import androidx.lifecycle.viewModelScope
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.record.shadows.*
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.manager.RecordStatusManager.RECORDING
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.junit.*
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import org.robolectric.annotation.LooperMode

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowScreenUtil::class,
        ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
@LooperMode(LooperMode.Mode.PAUSED)
class PopViewControllerTest {
    private var mActivityController: ActivityController<com.soundrecorder.record.RecorderActivity>? = null

    @Before
    fun setUp() {
        mActivityController = Robolectric.buildActivity(com.soundrecorder.record.RecorderActivity::class.java)
    }

    @Test
    fun should_not_null_popViewController_when_onCreate() {
        val activity = mActivityController?.create()?.get() ?: return
        val popViewController =
            Whitebox.getInternalState<PopViewController>(activity, "mPopViewController")
        Assert.assertNotNull(popViewController)
    }

    @Test
    fun should_not_null_popViewController_popViewDataCallback_when_onCreate() {
        val activity = mActivityController?.create()?.get() ?: return
        val popViewController =
            Whitebox.getInternalState<PopViewController>(activity, "mPopViewController")
        Assert.assertNotNull(popViewController)
        val popViewDataCallback =
            Whitebox.getInternalState<PopViewDataCallback>(popViewController, "popViewDataCallback")
        Assert.assertNotNull(popViewDataCallback)
    }

    @Test
    fun should_is_false_popViewController_isBackground_when_onCreate() {
        val activity = mActivityController?.create()?.get() ?: return
        val popViewController =
            Whitebox.getInternalState<PopViewController>(activity, "mPopViewController")
        Assert.assertNotNull(popViewController)
        val isBackground = Whitebox.getInternalState<Boolean>(popViewController, "isBackground")
        Assert.assertTrue(isBackground)
    }

    @Test
    @Ignore
    fun should_is_true_popViewController_isBackground_when_onStop() {
        val activity = mActivityController?.create()?.stop()?.get()
            ?: return
        val popViewController =
            Whitebox.getInternalState<PopViewController>(activity, "mPopViewController")
        Assert.assertNotNull(popViewController)
        val isBackground = Whitebox.getInternalState<Boolean>(popViewController, "isBackground")
        Assert.assertTrue(isBackground)
    }

    @Test
    @Ignore
    fun should_is_false_popViewController_isBackground_when_onStart() {
        val activity = mActivityController?.create()?.stop()?.start()?.get() ?: return
        val popViewController =
            Whitebox.getInternalState<PopViewController>(activity, "mPopViewController")
        Assert.assertNotNull(popViewController)
        val isBackground = Whitebox.getInternalState<Boolean>(popViewController, "isBackground")
        Assert.assertFalse(isBackground)
    }

    @Test
    fun should_null_popViewController_popViewDataCallback_when_onDestroy() {
        val activity = mActivityController?.create()?.destroy()?.get() ?: return
        val popViewController =
            Whitebox.getInternalState<PopViewController>(activity, "mPopViewController")
        Assert.assertNotNull(popViewController)
        val popViewDataCallback =
            Whitebox.getInternalState<PopViewDataCallback>(popViewController, "popViewDataCallback")
        Assert.assertNull(popViewDataCallback)
    }

    @Test
    fun check_default_FeatureOption_isSupportPhotoMarkRecommend() {
        val flag = FunctionOption.isSupportPhotoMarkRecommend()
        Assert.assertTrue(flag)
    }

    @Test
    @Ignore
    fun check_showPopView_when_do_not_takePhoto() {
        val activityController = mActivityController ?: return
        val service = Robolectric.buildService(RecorderService::class.java).create().get()
        val activity = activityController.create().get()
        activity.setRecorderService(service)
        service.setState(RECORDING)
        activityController.stop()
        val popViewController =
            Whitebox.getInternalState<PopViewController>(activity, "mPopViewController")
        val timeSliceManager =
            Whitebox.invokeMethod<PopTimeSliceManager>(popViewController, "getTimeSliceManager")
        Assert.assertTrue(timeSliceManager.getTimeSlices().size == 1)
        activityController.start()
        Assert.assertTrue(timeSliceManager.getTimeSlices().size == 1)
        val popViewWidget =
            Whitebox.getInternalState<PopViewWidget>(popViewController, "popViewWidget")
        Assert.assertNull(popViewWidget)
    }

    private fun RecorderService.setState(state: Int) {
        Whitebox.invokeMethod<Unit>(this, "setState", state)
    }

    private fun com.soundrecorder.record.RecorderActivity.setRecorderService(service: RecorderService) {
        Whitebox.setInternalState(this, "mRecorderService", service)
    }

    @WorkerThread
    private fun com.soundrecorder.record.RecorderActivity.takePhoto() {
        val cv = ContentValues()
        cv.put(
            MediaStore.Images.Media.DATA,
            "emulated\\0\\DCIM\\Camera\\IMG${System.currentTimeMillis()}.jpg"
        )
        cv.put(MediaStore.Images.Media.DATE_TAKEN, System.currentTimeMillis())
        cv.put(MediaStore.Images.Media.OWNER_PACKAGE_NAME, "com.oplus.camera")
        cv.put(MediaStore.Images.Media.RELATIVE_PATH, "DCIM\\Camera\\")
        contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, cv)
    }

    @Test
    @Ignore
    fun check_showPopView_when_takePhoto() {
        val activityController = mActivityController ?: return
        val service = Robolectric.buildService(RecorderService::class.java).create().get()
        val activity = activityController.create().get()
        activity.setRecorderService(service)
        service.setState(RECORDING)
        activityController.stop()
        val popViewController =
            Whitebox.getInternalState<PopViewController>(activity, "mPopViewController")
        val timeSliceManager =
            Whitebox.invokeMethod<PopTimeSliceManager>(popViewController, "getTimeSliceManager")
        Assert.assertTrue(timeSliceManager.getTimeSlices().size == 1)
        activity.takePhoto()
        activityController.start()
        Assert.assertTrue(timeSliceManager.getTimeSlices().size == 1)
        popViewController.viewModelScope.launch {
            delay(1000)
            val popViewWidget =
                Whitebox.getInternalState<PopViewWidget>(popViewController, "popViewWidget")
            Assert.assertNotNull(popViewWidget)
            Assert.assertTrue(popViewWidget.isAttachedToWindow)
        }
        popViewController.viewModelScope.launch {
            delay(6000)
            val popViewWidget =
                Whitebox.getInternalState<PopViewWidget>(popViewController, "popViewWidget")
            Assert.assertFalse(popViewWidget.isAttachedToWindow)
        }
    }

    @Test
    fun check_popView_when_showPopView_after_onBackPressed() {
        val activityController = mActivityController ?: return
        val activity = activityController.create().get()
        val popViewController =
            Whitebox.getInternalState<PopViewController>(activity, "mPopViewController")
        val data = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, 1)
        val timeSlice = PopTimeSlice(50, 10, 200, 160)
        Whitebox.invokeMethod<Unit>(
            popViewController,
            "showPopView",
            listOf(PopPicture(data, "1", "1", 100, emptyList(), timeSlice, 60))
        )
        popViewController.onBackPressed()
        popViewController.viewModelScope.launch {
            delay(1000)
            val popViewWidget =
                Whitebox.getInternalState<PopViewWidget>(popViewController, "popViewWidget")
            Assert.assertNull(popViewWidget)
            Assert.assertFalse(popViewWidget.isAttachedToWindow)
        }
    }

    @After
    fun tearDown() {
        mActivityController = null
    }
}