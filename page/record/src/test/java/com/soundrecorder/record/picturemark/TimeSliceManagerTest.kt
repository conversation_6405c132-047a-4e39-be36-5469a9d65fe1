/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: TimeSliceManagerTest
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.record.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class TimeSliceManagerTest {

    @Test
    fun should_timeSlices_is_empty_when_reset() {
        PopTimeSliceManager.reset()
        Assert.assertTrue(PopTimeSliceManager.getTimeSlices().isEmpty())
    }

    @Test
    fun should_timeSlices_is_empty_when_clear() {
        PopTimeSliceManager.clear()
        Assert.assertTrue(PopTimeSliceManager.getTimeSlices().isEmpty())
    }

    @Test
    fun check_getTimeSlices() {
        PopTimeSliceManager.reset()
        Assert.assertEquals(PopTimeSliceManager.getTimeSlices().size, 0)
        PopTimeSliceManager.startTimeSlice(1L)
        Assert.assertEquals(PopTimeSliceManager.getTimeSlices().size, 1)
        PopTimeSliceManager.endTimeSlice(2L)
        Assert.assertEquals(PopTimeSliceManager.getTimeSlices().size, 1)
        PopTimeSliceManager.startTimeSlice(3L)
        Assert.assertEquals(PopTimeSliceManager.getTimeSlices().size, 2)
        PopTimeSliceManager.endTimeSlice(4)
        Assert.assertEquals(PopTimeSliceManager.getTimeSlices().size, 2)
        PopTimeSliceManager.clear()
        Assert.assertEquals(PopTimeSliceManager.getTimeSlices().size, 0)
    }

    @After
    fun tearDown() {
        PopTimeSliceManager.clear()
    }
}