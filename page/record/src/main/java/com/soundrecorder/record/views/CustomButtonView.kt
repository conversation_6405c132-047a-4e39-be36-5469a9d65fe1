/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CustomButtonView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/7
 * * Author      : W9010241
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.record.views

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.record.R

class CustomButtonView : LinearLayout {

    companion object {
        private const val TAG = "CustomButtonView"
        const val DEFAULT_VALUE = -1
    }

    private var mButtonImage: ImageView? = null
    private var mButtonText: TextView? = null
    private var mLightImage = 0
    private var mUnLightImage = 0
    private var mLightColor = 0
    private var mUnLightColor = 0
    private var mResourceText = ""
    private var mEnable = false

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        val obtainStyledAttributes = context.obtainStyledAttributes(attrs, R.styleable.CustomButtonView)
        mLightImage = obtainStyledAttributes
                .getResourceId(R.styleable.CustomButtonView_button_light_img, DEFAULT_VALUE)
        mUnLightImage = obtainStyledAttributes
                .getResourceId(R.styleable.CustomButtonView_button_un_light_img, DEFAULT_VALUE)
        mLightColor = obtainStyledAttributes
            .getResourceId(
                R.styleable.CustomButtonView_button_light_text_color,
                com.support.appcompat.R.color.coui_color_secondary_neutral
            )
        mUnLightColor = obtainStyledAttributes
            .getResourceId(
                R.styleable.CustomButtonView_button_un_light_text_color,
                com.support.appcompat.R.color.coui_color_secondary_neutral
            )
        mEnable = obtainStyledAttributes.getBoolean(R.styleable.CustomButtonView_button_enable, false)
        mResourceText = obtainStyledAttributes.getString(R.styleable.CustomButtonView_button_text).toString()
        obtainStyledAttributes.recycle()
        initView()
    }

    private fun initView() {
        orientation = VERTICAL
        inflate(context, R.layout.layout_custom_button, this)
        mButtonImage = findViewById(R.id.buttonImage)
        mButtonText = findViewById(R.id.buttonText)
        setCustomEnable(mEnable)
        mButtonText?.text = mResourceText
    }

    fun setCustomEnable(enable: Boolean) {
        DebugUtil.d(TAG, "setCustomEnable enable == $enable")
        mEnable = enable
        mButtonImage?.setImageResource(if (enable) mLightImage else mUnLightImage)
        mButtonText?.setTextColor(if (enable) resources.getColor(mLightColor) else resources.getColor(mUnLightColor))
    }
}