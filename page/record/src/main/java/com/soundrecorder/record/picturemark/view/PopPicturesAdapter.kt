/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PopPicturesAdapter
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark.view

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.updatePaddingRelative
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.checkbox.COUICheckBox
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils
import com.soundrecorder.imageload.ImageLoaderUtils.into
import com.soundrecorder.record.R
import com.soundrecorder.record.databinding.ItemPopPictureBinding
import com.soundrecorder.record.picturemark.PopPicture
import com.soundrecorder.record.picturemark.view.PictureSelectActivity.Companion.ITEM_MARGIN
import com.soundrecorder.record.picturemark.view.PictureSelectActivity.Companion.MIN_IMAGE_WIDTH
import kotlin.math.abs

class PopPicturesAdapter : RecyclerView.Adapter<PopPicturesAdapter.PopViewHolder>() {
    companion object {
        private const val STATE_UNSELECT = -1   // 未选中
        private const val STATE_OVER_LIMIT = 0  // 达到标记上限
        private const val STATE_SELECT = 1    // 选中
        private const val STATE_DUPLICATE = 2 // 图片项与已选择项重复
        private const val STATE_DUPLICATE_WITH_MARKS = 3  // 图片项与文本标记重复
        private const val ONE_SECOND = 1000L
    }

    private val mPopPictures = ArrayList<PopPicture>()
    private val mImageLoadData = mutableListOf<ImageLoadData>()
    private var mOnItemClickListener: OnItemClickListener? = null
    private val mCheckboxState = mutableListOf<Int>()

    inner class PopViewHolder(
        private val mBinding: ItemPopPictureBinding
    ) : RecyclerView.ViewHolder(mBinding.root) {
        fun bind(item: ImageLoadData, state: Int, position: Int) {
            mBinding.ivPopPicture.into(item)
            when (state) {
                STATE_OVER_LIMIT, STATE_DUPLICATE, STATE_DUPLICATE_WITH_MARKS -> {
                    mBinding.root.alpha = 0.2f
                    mBinding.root.isClickable = false
                    mBinding.checkboxSelect.state = COUICheckBox.SELECT_NONE
                    mBinding.root.setOnClickListener {
                        ToastManager.showShortToast(
                            BaseApplication.getAppContext(),
                            if (state == STATE_OVER_LIMIT) {
                                com.soundrecorder.common.R.string.photo_mark_recommend_mark_limit
                            } else {
                                com.soundrecorder.common.R.string.photo_mark_recommend_duplicate
                            }
                        )
                    }
                }
                STATE_SELECT, STATE_UNSELECT -> {
                    mBinding.checkboxSelect.state = if (state == STATE_SELECT) {
                        COUICheckBox.SELECT_ALL
                    } else {
                        COUICheckBox.SELECT_NONE
                    }
                    mBinding.root.apply {
                        alpha = 1f
                        contentDescription = BaseApplication.getAppContext().resources.getString(
                            if (state == STATE_SELECT) {
                                com.soundrecorder.common.R.string.photo_mark_recommend_select
                            } else {
                                com.soundrecorder.common.R.string.photo_mark_recommend_unselect
                            }
                        )
                        isClickable = true
                        setOnClickListener {
                            mOnItemClickListener?.onClick(position)
                        }
                    }
                }
            }
        }

        fun clearImageView() {
            mBinding.ivPopPicture.setImageDrawable(null)
            mBinding.ivPopPicture.setImageBitmap(null)
        }
    }

    override fun onViewRecycled(holder: PopViewHolder) {
        super.onViewRecycled(holder)
        holder.clearImageView()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PopViewHolder {
        val holder = PopViewHolder(
            ItemPopPictureBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        )
        holder.itemView.updatePaddingRelative(ITEM_MARGIN, ITEM_MARGIN, ITEM_MARGIN, ITEM_MARGIN)
        return holder
    }

    override fun onBindViewHolder(holder: PopViewHolder, position: Int) {
        holder.bind(mImageLoadData[position], mCheckboxState[position], position)
    }

    override fun getItemCount(): Int = mPopPictures.size

    // 上限置灰未选中项
    fun disableUnselectItems() {
        for (i in 0 until mPopPictures.size) {
            if (mCheckboxState[i] == STATE_UNSELECT) {
                mCheckboxState[i] = STATE_OVER_LIMIT
                notifyItemChanged(i)
            }
        }
    }

    // 不满上限恢复置灰项
    fun enableAllItems() {
        for (i in mCheckboxState.indices) {
            if (mCheckboxState[i] == STATE_OVER_LIMIT) {
                mCheckboxState[i] = STATE_UNSELECT
                notifyItemChanged(i)
            }
        }
    }

    fun selectAllOrNone(isSelectAll: Boolean, selectedList: List<PopPicture>) {
        for (i in 0 until mPopPictures.size) {
            if ((isSelectAll && !selectedList.contains(mPopPictures[i])) || !isSelectAll) {
                mOnItemClickListener?.onClick(i)
            }
        }
    }

    fun restoreSelected(selectedList: List<PopPicture>) {
        if (selectedList.isEmpty()) return
        for (element in selectedList) {
            setCheckBoxState(mPopPictures.indexOf(element), true)
        }
    }

    fun setPopPictures(popPictures: List<PopPicture>, duplicatedItem: HashSet<Int>) {
        mPopPictures.clear()
        mCheckboxState.clear()
        mImageLoadData.clear()
        popPictures.forEachIndexed { index, popPicture ->
            mPopPictures.add(popPicture)
            mCheckboxState.add(if (index in duplicatedItem) STATE_DUPLICATE_WITH_MARKS else STATE_UNSELECT)
            mImageLoadData.add(ImageLoadData(popPicture.src(), MIN_IMAGE_WIDTH, MIN_IMAGE_WIDTH))
        }
        notifyItemRangeChanged(0, mPopPictures.size)
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        mOnItemClickListener = listener
    }

    fun setCheckBoxState(position: Int, value: Boolean) {
        val state = mCheckboxState[position]
        mCheckboxState[position] = if (value) {
            if (state == STATE_UNSELECT) {
                STATE_SELECT
            } else {
                STATE_UNSELECT
            }
        } else {
            STATE_UNSELECT
        }
        for (index in 0 until mPopPictures.size) {
            if (index != position && (mCheckboxState[index]) != STATE_DUPLICATE_WITH_MARKS && isItemDuplicateWithSelected(
                    mPopPictures[index],
                    mPopPictures[position]
                )
            ) {
                if (mCheckboxState[position] == STATE_SELECT) {
                    mCheckboxState[index] = STATE_DUPLICATE
                } else {
                    var flag = false
                    for (i in mCheckboxState.indices) {
                        if (mCheckboxState[i] == STATE_SELECT && isItemDuplicateWithSelected(
                                mPopPictures[index],
                                mPopPictures[i]
                            )
                        ) {
                            flag = true
                            break
                        }
                    }
                    if (!flag) mCheckboxState[index] = STATE_UNSELECT
                }
            }
        }
        notifyItemRangeChanged(0, mPopPictures.size)
    }

    private fun isItemDuplicateWithSelected(item: PopPicture, selectedItem: PopPicture): Boolean {
        return abs(item.recordTime - selectedItem.recordTime) < ONE_SECOND
    }

    fun clearCache() {
        mImageLoadData.forEach {
            ImageLoaderUtils.clearMemoryCacheByKey(it)
        }
    }

    interface OnItemClickListener {
        fun onClick(position: Int)
    }
}