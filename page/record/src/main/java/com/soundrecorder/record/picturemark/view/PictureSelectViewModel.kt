/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PictureSelectViewModel
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark.view

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.MarkSerializUtil
import com.soundrecorder.wavemark.mark.MarkHelper
import com.soundrecorder.record.picturemark.PopPicture

class PictureSelectViewModel : ViewModel() {

    companion object {
        private const val ONE_SECOND = 1000L
    }

    var popPictures = ArrayList<PopPicture>()
        set(value) {
            field.clear()
            value.sortWith { o1, o2 ->
                if (!hasDuplicateItems && kotlin.math.abs(o1.recordTime - o2.recordTime) < ONE_SECOND) {
                    hasDuplicateItems = true
                }
                (o1.dateTaken - o2.dateTaken).toInt()
            }
            field = if (value.size >= MarkHelper.MAX_MARKER_COUNT) {
                ArrayList(value.subList(0, MarkHelper.MAX_MARKER_COUNT))
            } else {
                value
            }
            if (field.size > maxMarkLimit) {
                hasDuplicateItems = true
            }
        }

    var marks = ArrayList<MarkDataBean>()
        set(value) {
            field.clear()
            field.addAll(value)
            maxMarkLimit = MarkHelper.MAX_MARKER_COUNT - field.size
        }

    var maxMarkLimit = 0

    // 图片列表是否有1s内连续的项，是则全选按钮无效
    var hasDuplicateItems = false

    var pictureSelectedList = MutableLiveData<MutableList<PopPicture>>()

    var buttonEnable = MutableLiveData(false)

    var isSelectAll = false

    fun addOrRemovePicture(position: Int) {
        val popPicture = popPictures[position]
        val originList = pictureSelectedList.value ?: arrayListOf()
        if (originList.contains(popPicture)) {
            originList.remove(popPicture)
        } else {
            originList.add(popPictures[position])
        }
        pictureSelectedList.value = originList
    }

    fun checkTimeDuplicate(position: Int): Boolean {
        return MarkSerializUtil.checkInTimeScopeInMarkList(
            marks,
            popPictures[position].recordTime
        ) == -1
    }

    fun getDuplicateItems(): HashSet<Int> {
        val result = HashSet<Int>()
        if (!marks.isNullOrEmpty()) {
            for (position in 0 until popPictures.size) {
                if (!checkTimeDuplicate(position)) {
                    result.add(position)
                }
            }
        }
        if (result.isNotEmpty()) hasDuplicateItems = true
        return result
    }
}