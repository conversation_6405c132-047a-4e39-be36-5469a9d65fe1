/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PopViewLoadingActivity
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.imageload.ImageLoaderUtils
import com.soundrecorder.imageload.utils.ImageUtils.uri2File
import com.soundrecorder.record.R
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class PopViewLoadingActivity : BaseActivity() {
    companion object {
        private const val SHOW_LOADING_SHORT_TIME = 1000L
        private const val KEY_POP_PICTURE_RESULT = "key_pop_picture_result"
        private var doMultiPictureMark: ((ArrayList<MarkMetaData>) -> Int)? = null
        private var doCompleteMultiPictureMark: (() -> Unit)? = null

        fun AppCompatActivity.startPopViewLoadingActivity(
            result: ArrayList<PopPicture>,
            doMultiPictureMark: (ArrayList<MarkMetaData>) -> Int,
            doCompleteMultiPictureMark: () -> Unit,
            requestCode: Int
        ) {
            <EMAIL> = doMultiPictureMark
            <EMAIL> = doCompleteMultiPictureMark
            lifecycleScope.launchWhenResumed {
                startActivityForResult(Intent().apply {
                    setClass(this@startPopViewLoadingActivity, PopViewLoadingActivity::class.java)
                    putParcelableArrayListExtra(KEY_POP_PICTURE_RESULT, result)
                }, requestCode)
                overridePendingTransition(0, 0)
            }
        }
    }

    private var mPictureSelectScope: Job? = null
    private val viewModel by lazy {
        ViewModelProvider(this).get(PopViewLoadingViewModel::class.java)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_pop_view_loading)
        ImageLoaderUtils.clearMemoryCache()
        var realWith = ScreenUtil.getRealScreenWidth()
        realWith = if (realWith >= ViewUtils.dp2px(360f + 2 * 48, false)) {
            ViewUtils.dp2px(360f, false).toInt()
        } else {
            ViewUtils.dp2px(360f - 2 * 48, false).toInt()
        }
        val attr = window.attributes
        attr.verticalMargin = 0f
        attr.horizontalMargin = 0f
        attr.width = realWith
        attr.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.attributes = attr
        window.decorView.setPadding(0, 0, 0, 0)
        if (savedInstanceState == null) {
            intent.getParcelableArrayListExtra<PopPicture>(KEY_POP_PICTURE_RESULT)?.let {
                viewModel.data.clear()
                viewModel.data.addAll(it)
            }
            addMultiPictureMark()
        }
        findViewById<View>(R.id.btnCancel).setOnClickListener {
            if (!ClickUtils.isFastDoubleClick()) {
                finishNotTransition()
            }
        }
        if (savedInstanceState != null && mPictureSelectScope?.isCompleted == true) {
            finishNotTransition()
        }
    }

    private fun addMultiPictureMark() {
        val startShowingTime = System.currentTimeMillis()
        viewModel.isShowing = viewModel.data.size > 1
        val selectPhotoData = ArrayList<MarkMetaData>()
        mPictureSelectScope = viewModel.viewModelScope.launch(IO) {
            for (element in viewModel.data) {
                val file = FileUtils.getAppFile()
                var imageParceResult = element.data.uri2File(file)
                if (imageParceResult != null) {
                    val markMetaData = MarkMetaData("", file.name, element.recordTime, imageParceResult.width, imageParceResult.height)
                    selectPhotoData.add(markMetaData)
                    val progress = (viewModel.data.indexOf(element) + 1) * 100 / viewModel.data.size
                    findViewById<COUIHorizontalProgressBar>(R.id.progress).setProgress(
                        progress,
                        true
                    )
                }
            }
            withContext(Main) {
                val dm = System.currentTimeMillis() - startShowingTime
                if (dm < SHOW_LOADING_SHORT_TIME) {
                    delay(SHOW_LOADING_SHORT_TIME - dm)
                }
                val successCount =
                    doMultiPictureMark?.invoke(selectPhotoData) ?: selectPhotoData.size
                val failedCount = viewModel.data.size - successCount
                finishNotTransition()
                viewModel.isShowing = false
                if (failedCount > 0) {
                    ToastManager.showShortToast(
                        this@PopViewLoadingActivity,
                        if (failedCount < viewModel.data.size) {
                            resources.getQuantityString(
                                com.soundrecorder.common.R.plurals.photo_mark_recommend_add_errors,
                                failedCount,
                                failedCount
                            )
                        } else {
                            resources.getString(com.soundrecorder.common.R.string.error_picture_mark_content)
                        }
                    )
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (isFinishing) {
            doCompleteMultiPictureMark?.invoke()
            doMultiPictureMark = null
            doCompleteMultiPictureMark = null
            mPictureSelectScope?.cancel()
        }
        ImageLoaderUtils.clearMemoryCache()
    }

    override fun onBackPressed() {
    }

    private fun finishNotTransition() {
        finish()
        overridePendingTransition(0, 0)
    }
}

class PopViewLoadingViewModel : ViewModel() {
    val data = mutableListOf<PopPicture>()
    var isShowing = false
}