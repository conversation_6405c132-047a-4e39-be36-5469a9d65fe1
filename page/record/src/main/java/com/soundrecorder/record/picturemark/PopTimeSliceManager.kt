/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PopTimeSliceManager
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark

import android.os.Parcelable
import androidx.annotation.NonNull
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.record.RecorderActivity
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import kotlinx.android.parcel.Parcelize
import java.util.concurrent.CopyOnWriteArrayList

/*
 * 智能图片标记，后台时间片管理工具
 */
object PopTimeSliceManager : RecorderControllerListener {
    private const val TAG = "PopTimeSliceManager"

    private val timeSlices = CopyOnWriteArrayList<PopTimeSlice>()

    fun addRecorderListener() {
        DebugUtil.d(TAG, "addListener")
        RecorderViewModelAction.addListener(this)
    }

    /*
     * 获取所有时间片数据
     */
    fun getTimeSlices(): List<PopTimeSlice> {
        DebugUtil.i(TAG, "timeSlices = $timeSlices")
        return timeSlices
    }

    /*
     * 开启一个时间片
     */
    fun startTimeSlice(startRecordTime: Long) {
        val timeSlice = PopTimeSlice(System.currentTimeMillis(), startRecordTime)
        timeSlices.add(timeSlice)
        DebugUtil.i(TAG, "startTimeSlice : $timeSlice")
    }

    /*
     * 结束一个时间片
     */
    fun endTimeSlice(endRecordTime: Long) {
        val timeSlice = timeSlices.lastOrNull()
        if (timeSlice == null || timeSlice.endTime > 0 || timeSlice.endRecordTime > 0) {
            DebugUtil.i(TAG, "endTimeSlice : 无效操作")
        } else {
            timeSlice.endTime = System.currentTimeMillis()
            timeSlice.endRecordTime = endRecordTime
            DebugUtil.i(TAG, "endTimeSlice : $timeSlice")
        }
    }

    @JvmStatic
    fun registerTimeSliceWhenNoRecorderActivity(
        @NonNull state: Int,
        @NonNull currentTimeMillis: Long
    ) {
        if (!FunctionOption.isSupportPhotoMarkRecommend()) {
            return
        }
        if (ActivityTaskUtils.getAppActivities().any { it is RecorderActivity }) {
            return
        }
        DebugUtil.i(TAG, "registerTimeSliceWhenNoRecorderActivity")
        when (state) {
            RecorderViewModelAction.RECORDING -> startTimeSlice(currentTimeMillis)
            RecorderViewModelAction.PAUSED -> endTimeSlice(currentTimeMillis)
        }
    }

    @JvmStatic
    fun unRegisterTimeSliceWhenNoRecorderActivity(
        @NonNull state: Int,
        @NonNull currentTimeMillis: Long
    ) {
        if (!FunctionOption.isSupportPhotoMarkRecommend()) {
            return
        }
        DebugUtil.i(TAG, "unRegisterTimeSliceWhenNoRecorderActivity")
        if (state == RecorderViewModelAction.RECORDING) {
            endTimeSlice(currentTimeMillis)
        }
    }

    fun reset() {
        timeSlices.clear()
        DebugUtil.i(TAG, "reset-timeSlices = $timeSlices")
    }

    /*
     * 清空时间片数据集
     */
    @JvmStatic
    fun clear() {
        timeSlices.clear()
        DebugUtil.i(TAG, "clear-timeSlices = $timeSlices")
    }

    fun onBindRecorderActivityOnCreate(currentStatus: Int) {
        DebugUtil.d(TAG, "onBindRecorderActivityOnCreate currentStatus: $currentStatus")
        unRegisterTimeSliceWhenNoRecorderActivity(
            currentStatus,
            RecorderViewModelAction.getAmplitudeCurrentTime()
        )
    }

    override fun onRecordStatusChange(state: Int) {
        DebugUtil.d(TAG, "onRecordStatusChange currentStatus: $state")
        registerTimeSliceWhenNoRecorderActivity(
            state,
            RecorderViewModelAction.getAmplitudeCurrentTime()
        )
    }

    override fun onCloseService() {
        DebugUtil.d(TAG, "onCloseService")
        clear()
    }
}

/*
 * 时间片数据封装类
 */
@Parcelize
data class PopTimeSlice(
    val startTime: Long,
    val startRecordTime: Long,
    var endTime: Long = 0,
    var endRecordTime: Long = 0
) : Parcelable