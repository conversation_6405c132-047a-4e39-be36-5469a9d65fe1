/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PopPictureManager
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark

import android.content.ContentUris
import android.net.Uri
import android.os.Parcelable
import android.provider.MediaStore
import android.provider.MediaStore.MediaColumns.*
import androidx.annotation.WorkerThread
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerData
import kotlinx.android.parcel.Parcelize

/**
 * 智能图片标记媒体库相机图片查询管理类
 */
object PopPictureManager {
    private const val TAG = "PopPictureManager"
    private const val CSHOT_PATH = "Cshot"

    @WorkerThread
    fun query(timeSlices: List<PopTimeSlice>, correctionTime: Long): List<PopPicture> {
        val popPictures = mutableListOf<PopPicture>()
        val contentResolver = BaseApplication.getAppContext().contentResolver
        val cShotMap = HashMap<String, MutableList<PopPicture>>()  // 连拍图片 path -> object
        val size = timeSlices.size
        for (index in 0 until size) {
            val timeSlice = timeSlices.getOrNull(index) ?: continue
            DebugUtil.i(TAG, "$timeSlice")
            /**
             * DATE_ADDED 单位秒
             * DATE_MODIFIED 单位秒
             * DATE_TAKEN 单位毫秒
             */
            val selection = "$RELATIVE_PATH LIKE ? AND $DATE_TAKEN > ? AND $DATE_TAKEN < ?"
            val selectionArgs = if (index == size - 1) {
                arrayOf(
                    "%Camera%",
                    "${timeSlice.startTime}",
                    "${timeSlice.endTime + correctionTime}"
                )
            } else {
                arrayOf("%Camera%", "${timeSlice.startTime}", "${timeSlice.endTime}")
            }

            kotlin.runCatching {
                contentResolver.query(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    null,
                    selection,
                    selectionArgs,
                    null
                )?.use {
                    val indexId = it.getColumnIndex(MediaStore.Images.Media._ID)
                    val indexRelativePath = it.getColumnIndex(RELATIVE_PATH)
                    val indexOwnerPackageName = it.getColumnIndex(OWNER_PACKAGE_NAME)
                    val indexDateTaken = it.getColumnIndex(DATE_TAKEN)
                    if (it.moveToFirst()) {
                        do {
                            DebugUtil.i(TAG, "$it")
                            val id = it.getLong(indexId)
                            val data = ContentUris.withAppendedId(
                                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                                id
                            )
                            val relativePath = it.getString(indexRelativePath)
                            var ownerPackageName: String? = ""
                            if (indexOwnerPackageName >= 0) {
                                ownerPackageName = it.getString(indexOwnerPackageName)
                            }
                            ownerPackageName = ownerPackageName.toString()
                            val dateTaken = it.getLong(indexDateTaken)
                            DebugUtil.i(
                                TAG,
                                "image createTime more than endTime = (${dateTaken - timeSlice.endTime})"
                            )
                            //图片对应的录音时间节点：图片创建时间 - 时间片开始时间 + 时间片录音开始录制时长
                            var recordTime = dateTaken - timeSlice.startTime + timeSlice.startRecordTime
                            if (recordTime > timeSlice.endRecordTime) {
                                recordTime = timeSlice.endRecordTime
                            }
                            if (relativePath.contains(CSHOT_PATH)) {    //连拍
                                if (!cShotMap.contains(relativePath)) {    //新的连拍文件夹
                                    val cShotList = mutableListOf<PopPicture>()
                                    val popPicture = PopPicture(
                                        data,
                                        relativePath,
                                        ownerPackageName.toString(),
                                        dateTaken,
                                        cShotList,
                                        timeSlice,
                                        recordTime
                                    )
                                    cShotMap[relativePath] = cShotList
                                    popPictures.add(popPicture)
                                } else {    //已有相同文件夹
                                    cShotMap[relativePath]?.add(
                                        PopPicture(
                                            data,
                                            relativePath,
                                            ownerPackageName.toString(),
                                            dateTaken,
                                            emptyList(),
                                            timeSlice,
                                            recordTime
                                        )
                                    )
                                }
                            } else {
                                popPictures.add(
                                    PopPicture(
                                        data,
                                        relativePath,
                                        ownerPackageName.toString(),
                                        dateTaken,
                                        emptyList(),
                                        timeSlice,
                                        recordTime
                                    )
                                )
                            }
                        } while (it.moveToNext())
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "query error: $it")
            }
        }
        DebugUtil.i(TAG, "$popPictures")
        return popPictures
    }
}

@Parcelize
data class PopPicture(
    // 图片文件路径
    val data: Uri,
    //相对路径
    val relativePath: String,
    // 文件拥有者
    val ownerPackageName: String,
    //创建时间
    val dateTaken: Long,
    //连拍同路径列表
    val cShotList: List<PopPicture>,
    //图片对应的时间片
    val timeSlice: PopTimeSlice,
    //图片对应的录音时间节点
    val recordTime: Long,
) : PhotoViewerData, Parcelable {
    override fun src(): Any {
        return data
    }
}