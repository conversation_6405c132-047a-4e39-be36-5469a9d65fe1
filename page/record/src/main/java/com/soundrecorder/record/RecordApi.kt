/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordApi
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.R
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.modulerouter.recorder.RecordAction
import com.soundrecorder.common.task.RecordRouterManager
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.recorderservice.*
import com.soundrecorder.recorderservice.manager.AudioModeChangeManager
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import oplus.multimedia.soundrecorder.BreenoStartRecordUtil

@Component(RecordAction.COMPONENT_NAME)
object RecordApi {

    private const val TAG = "RecorderApi"

    @Action(RecordAction.CREATE_RECORDER_INTENT)
    @JvmStatic
    fun createRecorderIntent(context: Context, isStatusBar: Boolean): Intent {
        val jumpIntent = Intent()
        jumpIntent.setClass(context, RecorderActivity::class.java)
        jumpIntent.putExtra(RecorderDataConstant.PLAYBACK_VIEWER_EXTRA_STATUSBAR, isStatusBar)
        return jumpIntent
    }

    @Action(RecordAction.CAN_START_RECORDER)
    @JvmStatic
    fun canStartRecord(checkIsCall: Boolean, comeFrom: String?): Boolean {
        if (checkIsCall && !AudioModeChangeManager.checkModeCanRecord()) {
            return false
        }

        val isAlreadyRecording = RecordStatusManager.isAlreadyRecording()
        val intercept = RecordRouterManager.getInstance().interceptStartRecord(comeFrom)
        DebugUtil.e(
            TAG,
            "startRecordActivity isAlreadyRecording：$isAlreadyRecording comFrom：$comeFrom intercept：$intercept"
        )
        if (isAlreadyRecording && intercept) {
            ToastManager.showShortToast(BaseApplication.getAppContext(), R.string.record_conflict)
            return false
        }

        return true
    }

    @Action(RecordAction.IS_RECORDER_ACTIVITY)
    @JvmStatic
    fun isRecorderActivity(context: Context): Boolean = context is RecorderActivity

    @Action(RecordAction.GET_RECORDER_ACTIVITY_CLASS)
    @JvmStatic
    fun getRecorderActivityClass(): Class<out Activity> {
        return RecorderActivity::class.java
    }

    @Action(RecordAction.REMOVE_RECORDING_ID)
    @JvmStatic
    fun removeRecordIdWhenRecordComplete(id: String?, keySet: String?): Boolean? {
        return RecorderService.removeRecordIdWhenRecordComplete(id, keySet)
    }

    @Action(RecordAction.SEND_START_FOREGROUND_SERVICE_BROADCAST)
    @JvmStatic
    fun sendStartForegroundServiceBroadCast() {
        BreenoStartRecordUtil.sendStartForegroundServiceBroadCast()
    }

    @Action(RecordAction.SEND_BROWSE_FRONT_TO_RECORD_BROADCAST)
    @JvmStatic
    fun sendBrowseFrontToRecordBroadCast() {
        BreenoStartRecordUtil.sendBrowseFrontToRecordBroadCast()
    }

    @Action(RecordAction.SEND_START_OTHER_PAGE_FAILED_BROADCAST)
    @JvmStatic
    fun sendStartOtherPageFailedBroadCast() {
        BreenoStartRecordUtil.sendStartOtherPageFailedBroadCast()
    }

    @Action(RecordAction.DO_CHECK_EXCEPTION_BRENO)
    @JvmStatic
    fun doCheckExceptionWhenStartRecordFromBreno(): Boolean? {
        return BreenoStartRecordUtil.doCheckExceptionWhenStartRecordFromBreeno()
    }

    /**
     * 获取录音照片与视频权限弹窗是否提示
     */
    @JvmStatic
    @Action(RecordAction.GET_SHOW_READ_IMAGE_PERMISSION_TIPS)
    fun getShowReadImagePermissionTips(): Boolean {
        return RecordImagesPermissionTips.mShowReadImagePermissionTips
    }

    /**
     * 设置录音照片与视频权限弹窗已经弹出过
     */
    @JvmStatic
    @Action(RecordAction.SET_SHOW_READ_IMAGE_PERMISSION_TIPS)
    fun setShowReadImagePermissionTips(show: Boolean) {
        RecordImagesPermissionTips.mShowReadImagePermissionTips = show
    }

    @Action(RecordAction.CREATE_NOTIFICATION_INTENT)
    @JvmStatic
    fun createNotificationIntent(context: Context): Intent? {
        if (RecorderViewModelAction.getCurrentStatus() == RecordStatusManager.RECORDING ||
            RecorderViewModelAction.getCurrentStatus() == RecordStatusManager.PAUSED) {
            val jumpIntent = Intent()
            jumpIntent.setClass(context, RecorderActivity::class.java)
            return jumpIntent
        } else {
            return BrowseFileAction.createBrowseFileIntent(context)
        }
    }
}