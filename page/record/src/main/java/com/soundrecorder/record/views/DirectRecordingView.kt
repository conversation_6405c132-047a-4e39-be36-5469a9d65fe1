/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DirectRecordingView
 * Description:
 * Version: 1.0
 * Date: 2024/6/17
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/6/17 1.0 create
 */

package com.soundrecorder.record.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.airbnb.lottie.LottieAnimationView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.record.R

/**
 * 录制页面定向录音开关按钮
 */
class DirectRecordingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : LinearLayout(context, attrs, defStyleAttr, defStyleRes) {
    private val logTag = "DirectRecordingView"
//    private var animView: EffectiveAnimationView? = null
    private var animView: LottieAnimationView? = null
    private var textView: TextView? = null
    var isDirectOn = false
        private set
    private var isDirectEnable = true
    private var currentAnimRes: Int? = null

    init {
        View.inflate(context, R.layout.view_direct_recording_view, this)
        orientation = HORIZONTAL
        animView = findViewById(R.id.record_enhance_img)
        textView = findViewById(R.id.record_enhance_text)
    }

    fun updateState(runAnim: Boolean = true, isDirectOn: Boolean, isEnable: Boolean) {
        if (isDirectOn == this.isDirectOn && isEnable == this.isDirectEnable) {
            DebugUtil.d(logTag, "updateState not change, isDirectOn=$isDirectOn,isEnable=$isEnable")
            return
        }
        DebugUtil.d(logTag, "updateState runAnim=$runAnim, isDirectOn=$isDirectOn,isEnable=$isEnable,this.isDirectOn=${this.isDirectOn}")
        if (isEnable) {
            val showAnim = runAnim && isDirectOn != this.isDirectOn
            if (isDirectOn) {
                textView?.text = context.getString(com.soundrecorder.common.R.string.specified_direct_record_close)
                if (showAnim) {
                    doDirectOnAnim()
                } else {
                    animView?.let {
                        setAnimationRawRes(it, R.raw.anim_direct_off)
                        // 避免上次播放完动效是最后一帧，本次期望显示第一帧
                        it.frame = 0
                    }
                }
            } else {
                textView?.text = context.getString(com.soundrecorder.common.R.string.specified_direct_record_open)
                if (showAnim) {
                    doDirectOffAnim()
                } else {
                    animView?.let {
                        setAnimationRawRes(it, R.raw.anim_direct_on)
                        it.frame = 0
                    }
                }
            }
        } else {
            release()
            background = ContextCompat.getDrawable(context, R.drawable.record_enhance_button_bg_disable)
            animView?.setImageResource(R.drawable.ic_direct_off)
            currentAnimRes = null
            textView?.setTextColor(ContextCompat.getColor(context, R.color.directional_button_text_disable_color))
            textView?.text = context.getString(com.soundrecorder.common.R.string.specified_direct_record_open)
        }
        this.isDirectOn = isDirectOn
        this.isDirectEnable = isEnable
    }

    private fun doDirectOnAnim() {
        animView?.let {
            if (it.isAnimating) {
                it.cancelAnimation()
            }
            setAnimationRawRes(it, R.raw.anim_direct_on)
            it.playAnimation()
        }
    }

    private fun setAnimationRawRes(view: LottieAnimationView, @androidx.annotation.RawRes resId: Int) {
        if (currentAnimRes != resId) {
            view.setAnimation(resId)
            currentAnimRes = resId
        }
    }

    private fun doDirectOffAnim() {
        animView?.let {
            if (it.isAnimating) {
                it.cancelAnimation()
            }
            setAnimationRawRes(it, R.raw.anim_direct_off)
            it.playAnimation()
        }
    }

    fun release() {
        animView?.apply {
            if (isAnimating) {
                cancelAnimation()
            }
            setImageDrawable(null)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        release()
    }
}