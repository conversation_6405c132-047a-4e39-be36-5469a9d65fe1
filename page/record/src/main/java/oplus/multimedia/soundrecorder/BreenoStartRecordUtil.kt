package oplus.multimedia.soundrecorder

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.modulerouter.EditRecordAction
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.recorderservice.*

object BreenoStartRecordUtil {

    const val TAG = "BreenoStartRecordUtil"

    @JvmStatic
    fun doCheckExceptionWhenStartRecordFromBreeno(): Boolean {
        DebugUtil.i(TAG, "doCheckExceptionWhenStartRecordFromBreeno")
        val storageCheckResult = checkStorageException()
        if (storageCheckResult != null) {
            sendStartFailedBroadCast(storageCheckResult)
            return false
        }
        val permissionCheckResult = checkPermissionException()
        if (permissionCheckResult != null) {
            sendStartFailedBroadCast(permissionCheckResult)
            return false
        }
        val alreadyRecordCheckResult = checkAlreadyRecordingException()
        if (alreadyRecordCheckResult != null) {
            sendStartFailedBroadCast(alreadyRecordCheckResult)
            return false
        }
        return true
    }

    /**
     * 检测是否存储空间是否够用，不够用返回不为空，
     */
    @JvmStatic
    private fun checkStorageException(): RecordResult? {
        var isStorageEnough = false
        try {
            isStorageEnough = RecorderViewModelAction.checkDistBeforeStartRecord()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "checkCanStartService checkStorageSpaceForRecordFile error", e)
        }
        DebugUtil.i(TAG, "checkStorageException isStorageEnough $isStorageEnough")
        if (!isStorageEnough) {
            return RecordResult(
                CURRENT_ACTION_STARTRECORD,
                CURRENT_RESULT_FAILED,
                ERROR_CODE_STARTRECORD_DISK_NOT_ENOUGH
            )
        }
        return null
    }


    /**
     * 检测是否存权限是否全部满足，满足则返回为空，
     */
    @JvmStatic
    private fun checkPermissionException(): RecordResult? {
        var isStoragePermissionGranted = false
        try {
            isStoragePermissionGranted = PermissionUtils.hasReadAudioPermission()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "checkCanStartService checkStorageSpaceForRecordFile error", e)
        }
        DebugUtil.i(
            TAG,
            "checkPermissionException isStoragePermissionGranted: $isStoragePermissionGranted"
        )
        if (!isStoragePermissionGranted) {
            return RecordResult(
                CURRENT_ACTION_STARTRECORD,
                CURRENT_RESULT_FAILED,
                ERROR_CODE_STARTRECORD_STORAGE_PERMISSION_NOT_GRANTED
            )
        }
        var isAudioPermissionGranted = false
        try {
            isAudioPermissionGranted = PermissionUtils.hasRecordAudioPermission()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "checkCanStartService checkStorageSpaceForRecordFile error", e)
        }
        DebugUtil.i(
            TAG,
            "checkPermissionException isAudioPermissionGranted: $isAudioPermissionGranted"
        )
        if (!isAudioPermissionGranted) {
            return RecordResult(
                CURRENT_ACTION_STARTRECORD,
                CURRENT_RESULT_FAILED,
                ERROR_CODE_STARTRECORD_RECORD_PERMISSION_NOT_GRANTED
            )
        }
        return null
    }


    /**
     * 检测是否存已经在录制过程中，不在录制过程中则返回为空，
     */
    @JvmStatic
    private fun checkAlreadyRecordingException(): RecordResult? {
        if (RecorderViewModelAction.isAlreadyRecording()) {
            return RecordResult(
                CURRENT_ACTION_STARTRECORD,
                CURRENT_RESULT_FAILED,
                ERROR_CODE_STARTRECORD_INVOKEERROR
            )
        }
        return null
    }


    /**
     * 发送开始录音失败广播给RecordStateService
     */
    @JvmStatic
    fun sendStartFailedBroadCast(recordResult: RecordResult) {
        DebugUtil.i(TAG, "sendStartFailedBroadCast recordResult $recordResult")
        val intent = Intent(RecordStateService.START_RECORD_FAILED_EXTRA)
        intent.putExtra(RecordStateService.START_RECORD_FAILED_EXTRA, recordResult)
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intent)
    }

    /**
     * 发送开始录音失败广播给RecordStateService
     * Trans入口进来有其它界面，无法录音
     */
    @JvmStatic
    fun sendStartOtherPageFailedBroadCast() {
        val recordResult = RecordResult(
            CURRENT_ACTION_STARTRECORD,
            CURRENT_RESULT_FAILED,
            ERROR_CODE_STARTRECORD_NOT_BROWSEFILE
        )
        DebugUtil.i(TAG, "sendStartOtherPageFailedBroadCast recordResult $recordResult")
        val intent = Intent(RecordStateService.START_RECORD_FAILED_EXTRA)
        intent.putExtra(RecordStateService.START_RECORD_FAILED_EXTRA, recordResult)
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intent)
    }

    /**
     * 发送开始启动录音前台服务广播给RecordStateService
     * 如果是录音前台启动，不需要RecordStateService start录音
     * 如果是无录音进程启动RecordService，类似侧边栏，需要RecordStateService调用start录音
     */
    @JvmStatic
    fun sendStartForegroundServiceBroadCast() {
        DebugUtil.i(TAG, "sendStartForegroundServiceBroadCast ")
        val intent = Intent(RecordStateService.START_AND_BIND_RECORDER_SERVICE_ACTION)
        var needStart = false
        if (RecorderViewModelAction.isFromBreno()) {
            needStart = true
        }
        intent.putExtra(RecordStateService.START_AND_BIND_EXTER, needStart)
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intent)
    }

    /**
     * 当前browse在前台，发送广播从首页跳转录音
     */
    @JvmStatic
    fun sendBrowseFrontToRecordBroadCast() {
        DebugUtil.i(TAG, "sendBrowseFrontToRecordBroadCast")
        val intent = Intent(RecordFileChangeNotify.BRENO_FRONT_TO_RECORD)
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intent)
    }

    /**
     * 返回录音栈所处当前页面
     */
    @JvmStatic
    fun getOtherStatus(recordStatus: RecordStatus): RecordStatus {
        val taskId = ActivityTaskUtils.getMainTaskId()
        if (ActivityTaskUtils.isFirstTaskPlaybackActivityOfAllTask() { act ->
                return@isFirstTaskPlaybackActivityOfAllTask ((BrowseFileAction.getBrowseFileClass()?.name == act.javaClass.name)
                        && (BrowseFileAction.hasPlayBackRecord(act)))
            }) { //先判断所有栈中是否有playActivity在栈顶
            //先判断所有栈中是否有playActivity在栈顶
            recordStatus.otherState = OTHER_STATE_PLAYBACK
        } else if (ActivityTaskUtils.isFirstTaskEditRecordActivityOfAllTask(
                EditRecordAction.getEditRecordActivityClass()?.name ?: ""
            )
        ) {
            //再判断所有栈中是否有editActivity在栈顶,此处如果有一个栈处于播放，有一个处于编辑，那么会优先返回播放
            recordStatus.otherState = OTHER_STATE_EDITRECORD
        } else if ((taskId == -1) || (ActivityTaskUtils.isTaskEmpty(taskId))) {
            //说明录音未启动过，没有录音栈
            recordStatus.otherState = OTHER_STATE_DEFAULT
        } else if (ActivityTaskUtils.isFirstTaskBrowseFileActivity(
                taskId,
                BrowseFileAction.getBrowseFileClass()?.name ?: ""
            )
        ) {
            //录音栈处于首页
            recordStatus.otherState = OTHER_STATE_DEFAULT
        } else if (ActivityTaskUtils.isSettingOrSecondaryActivity(taskId)) {
            //录音栈处于设置界面也可录制
            recordStatus.otherState = OTHER_STATE_DEFAULT
        } else {
            //说明录音当前处于其他不可录制界面
            recordStatus.otherState = OTHER_STATE_OTHERACTVITY
        }
        return recordStatus
    }

    /**
     * 返回录音当前播放状态
     */
    @JvmStatic
    fun getCurrentRecordStatus(recordStatus: RecordStatus): RecordStatus {
        when (RecorderViewModelAction.getCurrentStatus()) {
            RecorderViewModelAction.INIT -> recordStatus.currentRecordState = RECORD_STATE_NOTRECORD
            RecorderViewModelAction.RECORDING -> recordStatus.currentRecordState = RECORD_STATE_RECORDING
            RecorderViewModelAction.PAUSED -> recordStatus.currentRecordState = RECORD_STATE_PAUSED
        }
        return recordStatus
    }

    /**
     * 获取当前权限
     * 获取当前存储空间等错误状态
     */
    @JvmStatic
    fun getErrorStatus(recordStatus: RecordStatus): RecordStatus {
        DebugUtil.d(TAG, "getErrorStatus, recordStatus.otherState=${recordStatus.otherState}")
        if (isOtherCantRecordState(recordStatus.otherState)) {
            recordStatus.errorCode = STATUS_ERROR_CODE_DEFAULT
            return recordStatus
        }
        if (!PermissionUtils.hasReadAudioPermission() && !PermissionUtils.hasRecordAudioPermission()) {
            recordStatus.errorCode = ERROR_CODE_NOBOTHPERMISSION
        } else if (!PermissionUtils.hasReadAudioPermission()) {
            recordStatus.errorCode = ERROR_CODE_NOSTORAGEPERMISSION
        } else if (!PermissionUtils.hasRecordAudioPermission()) {
            recordStatus.errorCode = ERROR_CODE_NORECORDPERMISSION
        } else if (!RecorderViewModelAction.checkDistBeforeStartRecord()) {
            recordStatus.errorCode = ERROR_CODE_NO_SPACE
        } else if (!RecorderViewModelAction.checkModeCanRecord(false)) {
            recordStatus.errorCode = ERROR_CODE_INCALLSTATE
        } else {
            recordStatus.errorCode = STATUS_ERROR_CODE_DEFAULT
        }
        return recordStatus
    }

    @JvmStatic
    fun isOtherCantRecordState(otherState: Int): Boolean {
        return (otherState == OTHER_STATE_PLAYBACK) || (otherState == OTHER_STATE_EDITRECORD) || (otherState == OTHER_STATE_OTHERACTVITY)
    }
}