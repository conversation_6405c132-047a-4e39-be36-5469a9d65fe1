/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SmallCardManager
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */
// OPLUS Java File Skip Rule:MethodLength

package oplus.multimedia.soundrecorder.card.small

import android.app.RecoverableSecurityException
import android.os.Binder
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.oplus.recorderlog.util.CommonFlavor
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt
import com.soundrecorder.common.card.AppCardManager
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.modulerouter.AppCardAction
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.record.R
import oplus.multimedia.soundrecorder.card.CardUtils
import oplus.multimedia.soundrecorder.card.CardUtils.SMALL_CARD_VERSION_CODE_5
import oplus.multimedia.soundrecorder.card.small.ActivityAction.CLEAR_ALL_TASK
import oplus.multimedia.soundrecorder.card.small.ClickAction.CHECK_CAN_START_SERVICE
import java.util.UUID
import java.util.concurrent.CopyOnWriteArrayList

@Suppress("LongMethod", "TooGenericExceptionCaught")
object SmallCardManager : AppCardManager(), RecorderControllerListener {
    const val TAG = "SmallCardManager"
    private const val SMALL_CARD_TYPE = "${AppCardAction.CARD_TYPE_FOR_SMALL_CARD}&0&0"
    private const val ONE_PLUS_SMALL_CARD_TYPE = "${AppCardAction.CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD}&0&0"
    private const val RECOMMEND_SMALL_CARD_TYPE = "${AppCardAction.CARD_TYPE_FOR_RECOMMEND_SMALL_CARD}&0&0"
    private const val CARD_SWITCH_RECORDER_STATUS = "switch_recorder_status"
    private const val CHECK_RECORDER_PERMISSION = "check_recorder_permission"
    private const val CARD_ADD_TEXT_MARK = "add_text_mark"
    private const val CARD_SAVE_RECORDER_FILE = "save_recorder_file"
    private const val ON_VISIBLE = "onVisible"
    private const val ON_INVISIBLE = "onInVisible"
    private const val REFRESH_DATA = "refresh_data"
    private const val REFRESH_RATE = 2
    private const val DURATION_600 = 600L
    private const val DURATION_1500 = 1500L
    private const val TOKEN_NEED_RUN_MARK_AND_SAVE_ANIMATION = "tokenNeedRunMarkAndSaveAnimation"
    private const val TOKEN_CHECK_SHOW_MARK_NOTICE = "tokenCheckShowMarkNotice"
    private const val CUSTOM_DATA_ID = "wvRecord"
    private const val CUSTOM_DATA_TAG = "recordData"

    private val ctx by lazy { BaseApplication.getApplication() }
    private val widgetCodes = CopyOnWriteArrayList<String>()
    private val mainHandler = Handler(Looper.getMainLooper())
    private var cardUpdateCount = 0
    private var lastFileName = ""
    private var lastFileNameWithOutType = ""
    private var showMarkNotice = false
    private var showLoadingDialog = false
    private var showSuccessView = false
    private var needRunMarkAndSaveAnimation = false
    private var recordServiceUUID = UUID.randomUUID().toString()

    init {
        RecorderViewModelAction.addListener(this)
    }

    fun addWidgetCodes(widgetCode: String) {
        if (!widgetCodes.contains(widgetCode)) {
            widgetCodes.add(widgetCode)
            //冷启动时，需要在初次添加卡片强制刷新卡片数据
            postUpdateCommand(ctx, widgetCode)
        }
    }

    fun addWidgetCodesOnResume(widgetCode: String) {
        if (widgetCode.isBlank()) {
            return
        }
        if (!widgetCodes.contains(widgetCode)) {
            widgetCodes.add(widgetCode)
        }
        postUpdateCommand(ctx, widgetCode)
    }

    fun removeWidgetCodeOnPause(widgetCode: String) {
        widgetCodes.removeAll { it == widgetCode }
    }


    override fun getCustomDataId(): String {
        return CUSTOM_DATA_ID
    }

    override fun getCustomDataTag(): String {
        return CUSTOM_DATA_TAG
    }

    override fun getCustomData(widgetCode: String): String {
        try {
            val smallCardVersionCode = CardUtils.getSmallCardVersionCode()
            SmallCardDataUtil.smallCardVersionCode = smallCardVersionCode

            val alreadyRecording = RecorderViewModelAction.isAlreadyRecording()

            val data = if (RecorderViewModelAction.isFromOtherApp() && alreadyRecording) {
                setInitUi(widgetCode)
            } else {
                val originSaveState = RecorderViewModelAction.saveFileState
                val recordTime = RecorderViewModelAction.getAmplitudeCurrentTime()
                val isPausedAudioChange = RecorderViewModelAction.isAudioModeChangePause()

                // 录制状态值
                val recordStateData = SmallCardDataUtil.getRecordStateValue(originSaveState)
                val recordStateValue = recordStateData.first
                // 保存状态值(转成卡片数据)
                val saveStateValue = SmallCardDataUtil.getSaveStateValue(showSuccessView, originSaveState)
                // 录制时长TEXT、talkback、textColor
                val recordTextTalkDec = SmallCardDataUtil.getRecordTimeTextAndTalkDec(ctx, recordTime, recordStateValue)
                // 标记按钮enable、资源id、背景资源id
                val markButtonData = SmallCardDataUtil.getMarkButtonData()
                // 保存按钮enable、资源id、背景资源id
                val saveButtonData = SmallCardDataUtil.getSaveButtonData(originSaveState)
                // 录制按钮资源ID、talkback描述
               val recordButtonData =  SmallCardDataUtil.getRecordButtonData(ctx, recordStateData.second, originSaveState, isPausedAudioChange)
                // loadingView的Text
                val loadingData = SmallCardDataUtil.getSaveLoadingText(ctx, originSaveState)
                // 保存成功View对应的名称、成功提示语
                val saveSuccessData = SmallCardDataUtil.getSaveSuccessViewData(ctx, saveStateValue, lastFileName)
                // 波形相关数据：波形size、波形list数据、标记list数据
                val waveDataVersionCode3 = SmallCardDataUtil.getLastAmpListAndMarkList()
                // 保存的文件名。
                val saveResultText = ctx.getString(
                    com.soundrecorder.common.R.string.string_with_quote,
                    lastFileName.title() ?: ""
                )

                SmallCardData(
                    packageName = CommonFlavor.getInstance().getPackageName(),
                    widgetCode = widgetCode,
                    recordState = recordStateValue,
                    saveFileState = saveStateValue,
                    timeText = recordTextTalkDec.first,
                    timeTextColor = recordTextTalkDec.third,
                    timeDes = recordTextTalkDec.second,
                    stateText = "",
                    showMarkNotice = showMarkNotice,
                    markEnable = markButtonData.first,
                    isStartServiceFormAppCar = RecorderViewModelAction.isFromSmallCard(),
                    fileName = lastFileName,
                    fileNameWithOutType = saveSuccessData.first,
                    markSrc = markButtonData.second,
                    markBackGroundSrc = markButtonData.third,
                    recordInitSrc = recordButtonData.first,
                    recordPauseSrc = recordButtonData.first,
                    recordResumeSrc = recordButtonData.first,
                    saveFileSrc = saveButtonData.second,
                    saveBackGroundSrc = saveButtonData.third,
                    waveData = SmallCardDataUtil.getLastOneAmp(),
                    showLoadingDialog = showLoadingDialog,
                    loadingTitle = loadingData,
                    needRunMarkAndSaveAnimation = needRunMarkAndSaveAnimation,
                    recordServiceUUID = <EMAIL>,
                    markDesc = ctx.getString(com.soundrecorder.common.R.string.talkback_flag),
                    recordStateDesc = recordButtonData.second,
                    saveDesc = ctx.getString(com.soundrecorder.common.R.string.rename_save),
                    recordTimeMill = recordTime,
                    lastMarks = waveDataVersionCode3.third,
                    lastAmps = waveDataVersionCode3.second,
                    ampTotalSize = waveDataVersionCode3.first,
                    loadingTitleTalkBack = ctx.getString(com.soundrecorder.common.R.string.is_saving_talk_back),
                    versionCode = SmallCardDataUtil.getVersionCode()
                )
            }
            return GsonUtil.toJson(data)
        } catch (e: Exception) {
            return ""
        }
    }

    private fun setInitUi(widgetCode: String): SmallCardData {
        // 录制时长TEXT、talkback、textColor
        val recordTimeData = SmallCardDataUtil.getRecordTimeTextAndTalkDec(ctx, 0, RecorderState.INIT)
        val isVersion3OrLater = SmallCardDataUtil.isSmallVersion3OrLater()
        val recordInitSrc = if (isVersion3OrLater) R.drawable.breeno_card_record_init_new else R.drawable.breeno_card_record_init
        val recordPauseSrc = if (isVersion3OrLater) R.drawable.breeno_card_record_pause_new else R.drawable.breeno_card_record_pause
        val recordResumeSrc = if (isVersion3OrLater) R.drawable.breeno_card_record_recording_new else R.drawable.breeno_card_record_recording
        val markSrc = if (isVersion3OrLater) R.drawable.breeno_card_record_mark_new else R.drawable.breeno_card_record_mark
        val saveSrc = if (isVersion3OrLater) R.drawable.breeno_card_record_save_new else R.drawable.breeno_card_record_save

        return SmallCardData(
            packageName = CommonFlavor.getInstance().getPackageName(),
            widgetCode = widgetCode,
            recordState = RecorderState.INIT,
            saveFileState = SaveFileState.INIT,
            timeText = recordTimeData.first,
            timeTextColor = recordTimeData.third,
            timeDes = recordTimeData.second,
            stateText = ctx.getString(com.soundrecorder.common.R.string.recording_start),
            showMarkNotice = showMarkNotice,
            markEnable = false,
            isStartServiceFormAppCar = RecorderViewModelAction.isFromSmallCard(),
            fileName = "",
            fileNameWithOutType = "",
            markSrc = markSrc,
            markBackGroundSrc = R.drawable.small_card_no_ripple_bg,
            recordInitSrc = recordInitSrc,
            recordPauseSrc = recordPauseSrc,
            recordResumeSrc = recordResumeSrc,
            saveFileSrc = saveSrc,
            saveBackGroundSrc = R.drawable.small_card_no_ripple_bg,
            waveData = 0,
            showLoadingDialog = false,
            loadingTitle = ctx.getString(com.soundrecorder.common.R.string.saving),
            recordServiceUUID = this.recordServiceUUID,
            markDesc = ctx.getString(com.soundrecorder.common.R.string.talkback_flag),
            recordStateDesc = ctx.getString(com.soundrecorder.common.R.string.recording_start),
            saveDesc = ctx.getString(com.soundrecorder.common.R.string.rename_save),
            loadingTitleTalkBack = "",
            versionCode = SmallCardDataUtil.getVersionCode()
        )
    }

    fun parseMethod(method: String, widgetCode: String, isRecommendCard: Boolean = false): Bundle? {
        DebugUtil.i(TAG, "parseMethod = $method, widgetCode = $widgetCode, size = ${widgetCodes.size}")
        val fromOtherApp = RecorderViewModelAction.isFromOtherApp()
        when (method) {
            CHECK_RECORDER_PERMISSION -> {
                return Bundle().apply {
                    putBoolean("data", checkRecorderPermission())
                }
            }
            CHECK_CAN_START_SERVICE -> {
                /*入口来源： 卡片*/
                RecorderUserActionKt.sValueEntryFrom = RecorderUserAction.VALUE_ENTRY_FROM_CARD
                return Bundle().apply {
                    putBoolean("data", checkCanStartService())
                }
            }
            CLEAR_ALL_TASK -> {
                if (!fromOtherApp && !RecorderViewModelAction.hasInitRecorderService()) {
                    ActivityTaskUtils.clearAllTask()
                }
            }
            CARD_SWITCH_RECORDER_STATUS -> {
                if (!fromOtherApp) {
                    switchRecorderStatus()
                }
            }
            CARD_ADD_TEXT_MARK -> {
                if (!fromOtherApp) {
                    addTextMark()
                }
            }
            CARD_SAVE_RECORDER_FILE -> {
                if (!fromOtherApp) {
                    saveRecorderFile()
                }
            }
            ON_VISIBLE -> {
                addWidgetCodesOnResume(widgetCode)
                mainHandler.post {
                    refreshUI()
                }
            }
            ON_INVISIBLE -> {
                removeWidgetCodeOnPause(widgetCode)
                mainHandler.post {
                    refreshUI()
                }
            }
            REFRESH_DATA -> {
                return Bundle().apply {
                    putString("data", getCustomData(
                        if (isRecommendCard) {
                            RECOMMEND_SMALL_CARD_TYPE
                        } else {
                            if (FeatureOption.isOPExt()) ONE_PLUS_SMALL_CARD_TYPE else SMALL_CARD_TYPE
                        }))
                }
            }
        }
        return null
    }

    private fun checkCanStartService(): Boolean {
        val isNotInCall = RecorderViewModelAction.checkModeCanRecord(true)
        if (!isNotInCall) {
            return false
        }
        val isRecording = RecorderViewModelAction.isAlreadyRecording()
        DebugUtil.i(TAG, "isRecording = $isRecording")
        if (isRecording) {
            ToastManager.showShortToast(ctx, com.soundrecorder.common.R.string.record_conflict)
            return false
        }
        return true
    }

    private fun checkRecorderPermission(): Boolean {
        if (PermissionUtils.getNextAction() == PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            DebugUtil.i(TAG, "SHOULD_SHOW_USER_NOTICE")
            return false
        }
        if (!PermissionUtils.hasReadAudioPermission()) {
            DebugUtil.i(TAG, "hasReadAudioPermission")
            return false
        }
        if (!PermissionUtils.hasRecordAudioPermission()) {
            DebugUtil.i(TAG, "hasRecordAudioPermission")
            return false
        }
        return true
    }

    private fun addTextMark() {
        if (canAddTextMark()) {
            val token: Long = Binder.clearCallingIdentity()
            val mark = MarkMetaData(currentTimeMillis = RecorderViewModelAction.getAmplitudeCurrentTime())
            RecorderViewModelAction.addMark(mark)
            Binder.restoreCallingIdentity(token)
            BuryingPoint.addClickRecordTextMarkInSmallCard()
        } else {
            if (RecorderViewModelAction.checkMarkDataMoreThanMax()) {
                ToastManager.showShortToast(ctx, com.soundrecorder.common.R.string.photo_mark_recommend_mark_limit)
            }
        }
        refreshUI()
    }

    private fun canAddTextMark(): Boolean {
        return RecorderViewModelAction.isMarkEnabledFull()
    }

    private fun switchRecorderStatus() {
        if (RecorderViewModelAction.hasInitRecorderService()) {
            if (!RecorderViewModelAction.isAudioModeChangePause()) {
                RecorderViewModelAction.switchRecorderStatus(RecorderViewModelAction.FROM_SWITCH_RECORD_STATUS_SMALL_CARD)
            } else {
                ToastManager.showShortToast(ctx, com.soundrecorder.common.R.string.in_call_not_record)
                refreshUI()
            }
        } else {
            refreshUI()
        }
    }

    private fun saveRecorderFile() {
        if (RecorderViewModelAction.hasInitRecorderService()) {
            BuryingPoint.addClickSaveRecord(RecorderUserAction.VALUE_SAVE_RECORD_SMALL_CARD)
            RecorderViewModelAction.saveRecordInfo(saveRecordFromWhere = RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_APP_CARD)
        } else {
            refreshUI()
        }
    }

    fun onRequestPermissionCallBack(isMethodStartService: Boolean, hasPermission: Boolean) {
        //透明界面申请权限回调
        needRunMarkAndSaveAnimation = isMethodStartService && hasPermission
        DebugUtil.i(TAG, "onRequestPermissionCallBack $hasPermission, needRunMarkAndSaveAnimation = $needRunMarkAndSaveAnimation")
        if (needRunMarkAndSaveAnimation) {
            mainHandler.postDelayed({
                needRunMarkAndSaveAnimation = false
                refreshUI()
            }, TOKEN_NEED_RUN_MARK_AND_SAVE_ANIMATION, DURATION_600)
        }
        refreshUI()
    }

    override fun onReadyService() {
        refreshUI()
    }

    override fun onCloseService() {
        mainHandler.removeCallbacksAndMessages(TOKEN_NEED_RUN_MARK_AND_SAVE_ANIMATION)
        mainHandler.removeCallbacksAndMessages(TOKEN_CHECK_SHOW_MARK_NOTICE)
        recordServiceUUID = UUID.randomUUID().toString()
        showMarkNotice = false
        needRunMarkAndSaveAnimation = false
        cardUpdateCount = 0
        refreshUI()
    }

    override fun onWaveStateChange(state: Int) {
        if (RecorderViewModelAction.isFromOtherApp()) {
            return
        }
        refreshUI(state != RecorderViewModelAction.WaveState.UPDATE)
    }

    private fun refreshUI(forceUpdate: Boolean = true) {
        if (CardUtils.isSmallVersionCode2OrLater()) {
            CardUtils.refreshSmallCardData()
        } else {
            val size = widgetCodes.size
            if (size == 0) return
            if (forceUpdate || cardUpdateCount % (size * REFRESH_RATE) == 0) {
                val widgetIterator = widgetCodes.iterator()
                while (widgetIterator.hasNext()) {
                    widgetIterator.next()?.let {
                        postUpdateCommand(ctx, it)
                    }
                }
            }
            if (!forceUpdate) {
                cardUpdateCount++
            }
        }
    }

    override fun onSaveFileStateChange(
        state: Int,
        fileName: String,
        fullPath: String?,
        e: RecoverableSecurityException?
    ) {
        when (state) {
            RecorderViewModelAction.SaveFileState.START_LOADING -> {
                if ((CardUtils.getSmallCardVersionCode() >= SMALL_CARD_VERSION_CODE_5)) {
                    lastFileName = fileName
                }
            }

            RecorderViewModelAction.SaveFileState.SHOW_LOADING_DIALOG -> {
                lastFileName = fileName
                showLoadingDialog = true
            }
            RecorderViewModelAction.SaveFileState.SUCCESS -> {
                lastFileNameWithOutType = fileName.title() ?: fileName
                showLoadingDialog = false
                showSuccessView = true
                mainHandler.postDelayed({
                    showSuccessView = false
                    refreshUI()
                }, DURATION_1500)
            }
            else -> showLoadingDialog = false
        }
        refreshUI()
    }

    override fun onRecordStatusChange(state: Int) {
        refreshUI()
    }

    override fun onRecordCallConnected() {
        refreshUI()
    }

    override fun onMarkDataChange(markAction: Int, errorOrIndex: Int) {
        if (markAction == RecorderViewModelAction.MarkAction.SINGLE_ADD || markAction == RecorderViewModelAction.MarkAction.MULTI_ADD) {
            checkShowMarkNotice()
            refreshUI()
        }
    }

    private fun checkShowMarkNotice() {
        showMarkNotice = true
        mainHandler.postDelayed({
            showMarkNotice = false
            refreshUI()
        }, TOKEN_CHECK_SHOW_MARK_NOTICE, DURATION_600)
    }
}
