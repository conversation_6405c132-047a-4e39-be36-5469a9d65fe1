/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardApi
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/22 1.0 create
 */

package oplus.multimedia.soundrecorder.card.api

import android.os.Bundle
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.modulerouter.AppCardAction
import com.soundrecorder.modulerouter.AppCardAction.CARD_TYPE_FOR_DRAGON_FLY
import com.soundrecorder.modulerouter.AppCardAction.CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD
import com.soundrecorder.modulerouter.AppCardAction.CARD_TYPE_FOR_RECOMMEND_SMALL_CARD
import com.soundrecorder.modulerouter.AppCardAction.CARD_TYPE_FOR_SMALL_CARD
import com.soundrecorder.modulerouter.WidgetCodeAction.getCardType
import oplus.multimedia.soundrecorder.card.dragonFly.RecorderAppCardManager
import oplus.multimedia.soundrecorder.card.small.SmallCardManager

@Component(AppCardAction.COMPONENT_NAME)
object AppCardApi {

    @Action(AppCardAction.ADD_WIDGET_CODES)
    @JvmStatic
    fun addWidgetCodes(widgetCode: String) {
        when (widgetCode.getCardType()) {
            CARD_TYPE_FOR_DRAGON_FLY -> RecorderAppCardManager.addWidgetCodes(widgetCode)
            CARD_TYPE_FOR_RECOMMEND_SMALL_CARD, CARD_TYPE_FOR_SMALL_CARD, CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD -> {
                SmallCardManager.addWidgetCodes(widgetCode)
            }
            else -> {}
        }
    }

    @Action(AppCardAction.ADD_WIDGET_CODES_ON_RESUME)
    @JvmStatic
    fun addWidgetCodesOnResume(widgetCode: String) {
        when (widgetCode.getCardType()) {
            CARD_TYPE_FOR_DRAGON_FLY -> RecorderAppCardManager.addWidgetCodesOnResume(widgetCode)
            CARD_TYPE_FOR_RECOMMEND_SMALL_CARD, CARD_TYPE_FOR_SMALL_CARD, CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD -> {
                SmallCardManager.addWidgetCodesOnResume(widgetCode)
            }
            else -> {}
        }
    }

    @Action(AppCardAction.REMOVE_WIDGET_CODES_ON_PAUSE)
    @JvmStatic
    fun removeWidgetCodeOnPause(widgetCode: String) {
        when (widgetCode.getCardType()) {
            CARD_TYPE_FOR_DRAGON_FLY -> RecorderAppCardManager.removeWidgetCodeOnPause(widgetCode)
            CARD_TYPE_FOR_RECOMMEND_SMALL_CARD, CARD_TYPE_FOR_SMALL_CARD, CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD -> {
                SmallCardManager.removeWidgetCodeOnPause(widgetCode)
            }
            else -> {}
        }
    }

    @Action(AppCardAction.CALL_FROM_DRAGON_FLY_CARD)
    @JvmStatic
    fun callFromDragonFlyCard(method: String, widgetCode: String): Bundle? {
        return RecorderAppCardManager.parseMethod(method, widgetCode)
    }

    @Action(AppCardAction.CALL_FROM_SMALL_CARD)
    @JvmStatic
    fun callFromSmallCard(method: String, widgetCode: String, isRecommendCard: Boolean): Bundle? {
        return SmallCardManager.parseMethod(method, widgetCode, isRecommendCard)
    }
}