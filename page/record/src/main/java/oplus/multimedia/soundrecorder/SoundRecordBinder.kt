package oplus.multimedia.soundrecorder

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.Looper
import android.os.RemoteCallbackList
import android.os.RemoteException
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.executor.ExecutorManager
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.recorderservice.*
import com.soundrecorder.recorderservice.manager.listener.RecordResultCallback
import com.soundrecorder.recorderservice.recorder.binder.RecorderBinder
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

class SoundRecordBinder constructor(var recordStateService: RecordStateService) :
    ISoundRecordInterface.Stub() {

    companion object {
        const val TAG = "SoundRecordBinder"
        const val RECORD_SIZE_LIMIT = 500 * 1024 * 1024L
    }

    private var mWaitLock: ReentrantLock = ReentrantLock()
    private var mCondition = mWaitLock.newCondition()

    @Volatile
    private var mIsBind: Boolean = false
    private var mLastResult: RecordResult? = null
    private var mRemoteCallbackList: RemoteCallbackList<IRecordActionCallback> =
        RemoteCallbackList<IRecordActionCallback>()
    var mRecordResultCallback: RecordResultCallback = object : RecordResultCallback {
        override fun onRecordResultArrived(result: RecordResult) {
            DebugUtil.i(TAG, "onRecordResultArrived : $result")
            //停止录音
            if ((mLastResult != null)
                && (mLastResult?.currentAction == CURRENT_ACTION_STOPRECORD)
                && (result.currentAction == CURRENT_ACTION_SAVERECORD)
            ) {
                actionResult(mLastResult!!)
            } else if (result.currentAction != CURRENT_ACTION_STOPRECORD) {
                actionResult(result)
            }
            if (result.currentAction != CURRENT_ACTION_STOPRECORD) {
                //子线程
                unbindRecorderServiceAsync(recordStateService)
            }
            mLastResult = result
        }
    }


    @Volatile
    var mRecorderService: RecorderService? = null
    var mRecorderServiceConnection: RecorderServiceConnection = RecorderServiceConnection()


    override fun queryStatus(): RecordStatus {
        //check call permission
        recordStateService.enforceCallingPermission("oplus.permission.OPLUS_COMPONENT_SAFE", null)

        val recordStatus = RecordStatus()
        BreenoStartRecordUtil.getOtherStatus(recordStatus)
        BreenoStartRecordUtil.getCurrentRecordStatus(recordStatus)
        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        DebugUtil.d(TAG, "queryStatus")

        return recordStatus
    }

    /**
     * 继续录音，运行在binder线程池，非主线程
     */
    override fun resumeRecord() {
        //check call permission
        recordStateService.enforceCallingPermission("oplus.permission.OPLUS_COMPONENT_SAFE", null)

        DebugUtil.i(TAG, "AIDL resume record, recordService $mRecorderService")
        checkAndWaitBindRecordService()
        mRecorderService?.resume()
    }

    /**
     * 暂停录音，运行在binder线程池，非主线程
     */
    override fun pauseRecord() {
        //check call permission
        recordStateService.enforceCallingPermission("oplus.permission.OPLUS_COMPONENT_SAFE", null)

        DebugUtil.i(TAG, "AIDL pause record, recordService $mRecorderService")
        checkAndWaitBindRecordService()
        mRecorderService?.pause()
    }

    /**
     * 停止录音，运行在binder线程池，非主线程
     */
    override fun stopRecord() {
        //check call permission
        recordStateService.enforceCallingPermission("oplus.permission.OPLUS_COMPONENT_SAFE", null)

        DebugUtil.i(TAG, "AIDL stop record, recordService $mRecorderService")
        checkAndWaitBindRecordService()
        mRecorderService?.stop()
        mRecorderService?.saveRecordInfo("", "", true, RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_XIAO_BU)
    }

    /**
     * 注册AIDL结果回调，运行在binder线程池，非主线程
     */
    override fun registerCallback(callback: IRecordActionCallback?): Boolean {
        //check call permission
        recordStateService.enforceCallingPermission("oplus.permission.OPLUS_COMPONENT_SAFE", null)

        val registered = mRemoteCallbackList.register(callback)
        DebugUtil.i(TAG, "registerCallback $callback, registered $registered")
        return registered
    }

    /**
     * 解注册AIDL结果回调，运行在binder线程池，非主线程
     */
    override fun unRegisterCallback(callback: IRecordActionCallback?): Boolean {
        //check call permission
        recordStateService.enforceCallingPermission("oplus.permission.OPLUS_COMPONENT_SAFE", null)

        val unregistered = mRemoteCallbackList.unregister(callback)
        DebugUtil.i(TAG, "unRegisterCallback $callback, unregistered $unregistered")
        return unregistered
    }


    /**
     * 回调AIDL结果，运行在binder线程池，不能在主线程中调用
     */
    private fun actionResult(result: RecordResult) {
        val n = mRemoteCallbackList.beginBroadcast()
        DebugUtil.i(TAG, "actionResult begin $n")
        for (i in 0 until n) {
            val actionCallback: IRecordActionCallback? =
                mRemoteCallbackList.getRegisteredCallbackItem(i)
            try {
                actionCallback?.onRecordResult(result)
                DebugUtil.i(TAG, "actionResult onRecordResult $result")
            } catch (e: RemoteException) {
                DebugUtil.e(TAG, "actionResult error", e)
            }
        }
        mRemoteCallbackList.finishBroadcast()
    }


    /**
     * 回调AIDL结果，加上线程转换，该方法可以在主线程或子线程调用皆可
     */
    fun postResult(result: RecordResult) {
        DebugUtil.i(TAG, "postResult $result")
        if (Looper.getMainLooper() == Looper.myLooper()) {
            ExecutorManager.singleExecutor?.execute {
                actionResult(result)
            }
        } else {
            actionResult(result)
        }
    }

    private fun checkAndWaitBindRecordService() {
        DebugUtil.i(TAG, "checkAndWaitBindRecordService")
        mWaitLock.withLock {
            if (mRecorderService == null) {
                //binder子线程
                bindRecorderServiceAsync(recordStateService)
                mCondition.await()
            } else {
                DebugUtil.i(
                    TAG,
                    "checkAndWaitBindRecordService mRecordService not null, no need to wait"
                )
            }
        }
    }


    fun unbindRecorderServiceAsync(context: Context) {
        ExecutorManager.singleExecutor?.execute {
            unbindRecorderService(context)
        }
    }

    fun bindRecorderServiceAsync(context: Context, needStartRecordAfterBind: Boolean = false) {
        ExecutorManager.singleExecutor?.execute {
            bindRecorderService(context, needStartRecordAfterBind)
        }
    }

    private fun unbindRecorderService(context: Context) {
        try {
            if (!mIsBind) {
                DebugUtil.e(TAG, "unbindRecorderService already unbind, no need unbind again")
                return
            }
            DebugUtil.d(TAG, " unbindRecorderService ")
            context.applicationContext.unbindService(mRecorderServiceConnection)
            mIsBind = false
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun bindRecorderService(context: Context, needStartRecordAfterBind: Boolean = false) {
        try {
            DebugUtil.i(
                TAG,
                "bindRecorderService mIsBind $mIsBind, context $context" +
                        ", needStartRecordAfterBind $needStartRecordAfterBind, SoundRecordBinder $this"
            )
            if (mIsBind) {
                DebugUtil.e(TAG, "bindRecorderService already bind, no need bind again")
                return
            }
            var intent = Intent("oplus.intent.action.RECORDER_SERVICE")
            intent.setPackage(context.packageName)
            intent.putExtra(RecorderDataConstant.BIND_EXTRA_NEED_START_RECORD_AFTER_BIND, needStartRecordAfterBind)
            DebugUtil.i(TAG, " bindRecorderService ")
            context.applicationContext.bindService(
                intent,
                mRecorderServiceConnection,
                Context.BIND_AUTO_CREATE
            )
            mIsBind = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    inner class RecorderServiceConnection : ServiceConnection {

        override fun onServiceConnected(p0: ComponentName?, p1: IBinder?) {
            DebugUtil.i(TAG, "onRecorderServiceConnected")
            mWaitLock.withLock {
                mRecorderService = (p1 as RecorderBinder).service
                if (mRecorderService == null) {
                    DebugUtil.i(TAG, "onServiceConnected, RecorderService is null")
                    return
                }
                mRecorderService?.registerRecordResultCallback(mRecordResultCallback)
                mCondition.signalAll()
            }

            DebugUtil.i(
                TAG, "onRecorderServiceConnected currentRecordStatus: "
                        + RecorderViewModelAction.getCurrentStatus()
                        + ", needStartAfterBind: ${mRecorderService?.needStartAfterBind ?: true}"
            )
            if (mRecorderService?.needStartAfterBind != false) {
                if (RecorderViewModelAction.getCurrentStatus() == RecorderViewModelAction.HALT_ON
                    || RecorderViewModelAction.getCurrentStatus() == RecorderViewModelAction.INIT
                ) {
                    mRecorderService?.start()
                } else {
                    DebugUtil.e(
                        TAG,
                        "onRecorderServiceConnected currentRecordStatus : " + RecorderViewModelAction.getCurrentStatus()
                    )
                }
            }
        }

        override fun onServiceDisconnected(p0: ComponentName?) {
            DebugUtil.i(TAG, "onRecorderServiceDisconnected")
            mRecorderService?.unRegisterResultCallback()
            mWaitLock.withLock {
                mRecorderService = null
                mCondition.signalAll()
            }
        }
    }
}