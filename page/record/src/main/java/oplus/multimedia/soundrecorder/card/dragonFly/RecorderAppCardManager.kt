/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderDataPack
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(v-zhengt<PERSON><PERSON><EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package oplus.multimedia.soundrecorder.card.dragonFly

import android.app.RecoverableSecurityException
import android.graphics.Color
import android.os.Binder
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.oplus.recorderlog.util.CommonFlavor
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.card.AppCardManager
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.modulerouter.AppCardAction
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.PAUSED
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.RECORDING
import com.soundrecorder.record.R
import oplus.multimedia.soundrecorder.card.CardUtils
import java.util.concurrent.CopyOnWriteArrayList

@Suppress("LongMethod", "TooGenericExceptionCaught")
object RecorderAppCardManager : AppCardManager(), RecorderControllerListener {
    const val CARD_TAG = "RecorderAppCardTag"
    private const val DRAGON_FLY_CARD_TYPE = "${AppCardAction.CALL_FROM_DRAGON_FLY_CARD}&0&0"
    private const val CARD_SWITCH_RECORDER_STATUS = "switch_recorder_status"
    private const val CHECK_RECORDER_PERMISSION = "check_recorder_permission"
    private const val CARD_ON_IN_VISIBLE = "on_in_visible"
    private const val CARD_ON_VISIBLE = "on_visible"
    private const val CARD_ADD_TEXT_MARK = "add_text_mark"
    private const val CARD_SAVE_RECORDER_FILE = "save_recorder_file"
    private const val CHECK_START_SERVICE = "check_start_service"
    private const val REFRESH_DATA = "refresh_data"
    private const val REFRESH_RATE = 2
    private const val DURATION_1500 = 1_500L
    private const val DELAY_150 = 150L
    private const val UN_ENABLE_CALL = -1
    private const val ENABLE = 0
    private const val WAVE_MAX_SIZE = 50
    private const val CUSTOM_DATA_ID = "AppCardEngineView"
    private const val CUSTOM_DATA_TAG = "data"
    private val ctx by lazy { BaseApplication.getAppContext() }
    private val widgetCodes = CopyOnWriteArrayList<String>()
    private val mainHandler = Handler(Looper.getMainLooper())

    private var cardUpdateCount = 0
    private var lastFileName = ""
    private var showMarkNotice = false
    private var showNoMarkNotice = false
    private var showDisConnectedCallNotice = false
    private var showConnectedCallNotice = false
    private var showMuteNotice = false
    private var showInCallNotice = false

    /**
     * localUnStandardSaveState不会再onSaveFileStateChange回调中置为INIT
     * state变为SUCCESS后，最快30ms后service就执行onDestory了，saveState被置为INIT
     * 避免Success后卡片过来拿数据，拿到的是INIT，导致没有弹保存成功弹窗;
     */
    private var localUnStandardSaveState = RecorderViewModelAction.saveFileState

    init {
        if (FeatureOption.isHasSupportDragonfly()) {
            RecorderViewModelAction.addListener(this)
        }
    }

    fun addWidgetCodes(widgetCode: String) {
        if (!widgetCodes.contains(widgetCode)) {
            widgetCodes.add(widgetCode)
            //冷启动时，需要在初次添加卡片强制刷新卡片数据
            postUpdateCommand(ctx, widgetCode)
        }
    }

    fun addWidgetCodesOnResume(widgetCode: String) {
        if (widgetCode.isEmpty()) return
        if (!widgetCodes.contains(widgetCode)) {
            widgetCodes.add(widgetCode)
        }
        postUpdateCommand(ctx, widgetCode)
    }

    fun removeWidgetCodeOnPause(widgetCode: String) {
        if (widgetCode.isEmpty()) return
        widgetCodes.removeAll { it == widgetCode }
    }

    override fun getCustomDataId(): String {
        return CUSTOM_DATA_ID
    }

    override fun getCustomDataTag(): String {
        return CUSTOM_DATA_TAG
    }

    override fun getCustomData(widgetCode: String): String {
        try {
            val time = RecorderViewModelAction.getAmplitudeCurrentTime()
            var stateText = ""
            var recordStateSrc: Int
            var recordStateRippleSrc = R.drawable.dragon_fly_ripple_bg
            val recordState: Int
            val recordStateDesc: String
            val saveFileStateOrigin = getSaveFileState()
            val isSavingState = isSavingState(saveFileStateOrigin)
            val recordStatus = if (isSavingState) {
                RecorderViewModelAction.getRecordStatusBeforeSaving()
                    ?: RecorderViewModelAction.getCurrentStatus()
            } else {
                RecorderViewModelAction.getCurrentStatus()
            }
            when (recordStatus) {
                RECORDING -> {
                    recordStateSrc = R.drawable.ic_record_recording
                    recordState = RecorderState.RECORDING
                    recordStateDesc = ctx.getString(com.soundrecorder.common.R.string.recording_notify_talk_back)
                }
                PAUSED -> {
                    recordStateSrc = R.drawable.ic_record_pause
                    recordState = RecorderState.PAUSED
                    recordStateDesc = ctx.getString(com.soundrecorder.common.R.string.record_pause_tips)
                }
                else -> {
                    recordStateSrc = R.drawable.ic_record_init
                    recordState = RecorderState.INIT
                    recordStateDesc = ctx.getString(com.soundrecorder.common.R.string.recording_start)
                }
            }
            val saveFileState = getSaveStateAndText(saveFileStateOrigin).run {
                second?.let {
                    stateText = it
                }
                return@run first
            }

            if (showNoMarkNotice) {
                stateText = ctx.getString(com.soundrecorder.common.R.string.photo_mark_recommend_mark_limit)
            } else if (showMarkNotice) {
                stateText = ctx.getString(
                    com.soundrecorder.common.R.string.notification_marked,
                    TimeUtils.getFormatTimeExclusiveMill(RecorderViewModelAction.getLastMarkTime())
                )
            }

            var markEnabled = true
            var saveEnabled = true
            var switchEnabled = true
            var markSrc: Int
            var markRippleSrc: Int
            val saveFileSrc: Int
            val saveFileRippleSrc: Int
            if (RecorderViewModelAction.isMarkEnabledFull()) {
                markSrc = R.drawable.ic_mark_normal
                markRippleSrc = R.drawable.dragon_fly_ripple_bg
            } else {
                markSrc = R.drawable.ic_mark_disable
                markRippleSrc = R.drawable.dragon_fly_no_ripple_bg
                markEnabled = false
            }

            // 通话中断场景
            if (!isSavingState && RecorderViewModelAction.isAudioModeChangePause()) {
                recordStateSrc = R.drawable.ic_record_pause_disabled
                recordStateRippleSrc = R.drawable.dragon_fly_no_ripple_bg
                switchEnabled = false
            }

            if (isSavingState) {
                saveFileSrc = R.drawable.ic_save_disable
                saveFileRippleSrc = R.drawable.dragon_fly_no_ripple_bg

                markSrc = R.drawable.ic_mark_disable
                markRippleSrc = R.drawable.dragon_fly_no_ripple_bg
                recordStateRippleSrc = R.drawable.dragon_fly_no_ripple_bg
                markEnabled = false
                switchEnabled = false
                saveEnabled = false
            } else {
                saveFileSrc = R.drawable.ic_save_normal
                saveFileRippleSrc = R.drawable.dragon_fly_ripple_bg
            }

            if (showConnectedCallNotice) {
                stateText = ctx.getString(com.soundrecorder.common.R.string.call_record_pause)
            }

            if (showDisConnectedCallNotice) {
                stateText = ctx.getString(com.soundrecorder.common.R.string.call_record_resuem)
            }

            if (showMuteNotice) {
                stateText = ctx.getString(com.soundrecorder.common.R.string.record_mute_tips)
            }

            if (showInCallNotice) {
                stateText = ctx.getString(com.soundrecorder.common.R.string.in_call_not_record)
            }
            val amps = RecorderViewModelAction.getAmplitudeList()
            return GsonUtil.toJson(
                AppCardData(
                    packageName = CommonFlavor.getInstance().getPackageName(),
                    widgetCode = widgetCode,
                    recordState = recordState,
                    saveFileState = saveFileState,
                    timeText = TimeUtils.getFormatTimeByMillisecond(time),
                    timeTextColor = ctx.getColor(R.color.dragon_fly_time_text_color),
                    isFakeBoldText = true,
                    timeDes = TimeUtils.getDurationHint(ctx, time),
                    stateText = stateText,
                    recorderName = ctx.getString(com.soundrecorder.common.R.string.app_name_main),
                    stateTextColor = ctx.getColor(R.color.dragon_fly_state_Text_Color_Normal),
                    fileName = lastFileName,
                    markSrc = markSrc,
                    recordStateSrc = recordStateSrc,
                    saveFileSrc = saveFileSrc,
                    markRippleSrc = markRippleSrc,
                    stateRippleSrc = recordStateRippleSrc,
                    saveRippleSrc = saveFileRippleSrc,
                    ampsSize = amps.size,
                    lastAmps = amps.run {
                        if (size <= WAVE_MAX_SIZE) {
                            this
                        } else {
                            subList(size - WAVE_MAX_SIZE, size)
                        }.reversed()
                    },
                    cardColor = Color.parseColor("#FAFAFA"),
                    cardWaveColor = Color.parseColor("#D9000000"),
                    cardDashWaveColor = Color.parseColor("#D9666666"),
                    markEnabled = markEnabled,
                    switchEnabled = switchEnabled,
                    saveEnabled = saveEnabled,
                    markDesc = ctx.getString(com.soundrecorder.common.R.string.talkback_flag),
                    recordStateDesc = recordStateDesc,
                    saveDesc = ctx.getString(com.soundrecorder.common.R.string.rename_save)
                )
            )
        } catch (e: Exception) {
            return ""
        }
    }

    fun parseMethod(method: String, widgetCode: String): Bundle? {
        DebugUtil.i(CARD_TAG, "method = $method,widgetCode = $widgetCode")
        when (method) {
            CARD_ON_VISIBLE -> addWidgetCodesOnResume(widgetCode)
            CARD_ON_IN_VISIBLE -> removeWidgetCodeOnPause(widgetCode)
            CHECK_RECORDER_PERMISSION -> {
                return Bundle().apply {
                    putBoolean("data", checkRecorderPermission())
                }
            }
            CARD_SWITCH_RECORDER_STATUS -> switchRecorderStatus()
            CARD_ADD_TEXT_MARK -> addTextMark()
            CARD_SAVE_RECORDER_FILE -> saveRecorderFile()
            CHECK_START_SERVICE -> {
                return Bundle().apply {
                    var value = ENABLE
                    if (!RecorderViewModelAction.checkModeCanRecord(false)) {
                        checkShowInCallNotice()
                        value = UN_ENABLE_CALL
                    }
                    putInt("data", value)
                }
            }
            REFRESH_DATA -> {
                return Bundle().apply {
                    putString("data", getCustomData(DRAGON_FLY_CARD_TYPE))
                }
            }
        }
        return null
    }

    private fun getSaveStateAndText(saveStateOrigin: Int): Pair<Int, String?> {
        val saveFileState: Int
        val stateText: String?
        when (saveStateOrigin) {
            RecorderViewModelAction.SaveFileState.START_LOADING -> {
                stateText = ctx.getString(com.soundrecorder.common.R.string.is_saving)
                saveFileState = SaveFileState.START_LOADING
            }
            RecorderViewModelAction.SaveFileState.SHOW_LOADING_DIALOG -> {
                stateText = ctx.getString(com.soundrecorder.common.R.string.is_saving)
                saveFileState = SaveFileState.SHOW_DIALOG
            }
            RecorderViewModelAction.SaveFileState.SUCCESS -> {
                stateText = ctx.getString(com.soundrecorder.common.R.string.is_saving)
                saveFileState = SaveFileState.SUCCESS
            }
            RecorderViewModelAction.SaveFileState.ERROR -> {
                stateText = ctx.getString(com.soundrecorder.common.R.string.is_saving)
                saveFileState = SaveFileState.ERROR
            }
            else -> {
                saveFileState = SaveFileState.INIT
                stateText = null
            }
        }
        return Pair(saveFileState, stateText)
    }

    private fun isSavingState(saveStateOrigin: Int): Boolean = saveStateOrigin != RecorderViewModelAction.SaveFileState.INIT

    private fun checkRecorderPermission(): Boolean {
        ActivityTaskUtils.clearAllTask()
        if (PermissionUtils.getNextAction() == PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            DebugUtil.i(CARD_TAG, "SHOULD_SHOW_USER_NOTICE")
            return false
        }
        if (!PermissionUtils.hasReadAudioPermission()) {
            DebugUtil.i(CARD_TAG, "hasReadAudioPermission")
            return false
        }
        if (!PermissionUtils.hasRecordAudioPermission()) {
            DebugUtil.i(CARD_TAG, "hasRecordAudioPermission")
            return false
        }
        return true
    }

    private fun addTextMark() {
        if (!RecorderViewModelAction.hasInitRecorderService()) {
            refreshUI()
            return
        } else if (RecorderViewModelAction.checkMarkDataMoreThanMax()) {
            checkShowNoMarkNotice()
            return
        } else if (!RecorderViewModelAction.isMarkEnabledFull()) {
            return
        } else {
            val token: Long = Binder.clearCallingIdentity()
            val mark = MarkMetaData(currentTimeMillis = RecorderViewModelAction.getAmplitudeCurrentTime())
            RecorderViewModelAction.addMark(mark)
            Binder.restoreCallingIdentity(token)
        }
    }

    private fun switchRecorderStatus() {
        if (RecorderViewModelAction.hasInitRecorderService()) {
            if (!RecorderViewModelAction.isAudioModeChangePause()) {
                RecorderViewModelAction.switchRecorderStatus(RecorderViewModelAction.FROM_SWITCH_RECORD_STATUS_APP_CARD)
            } else {
                checkShowInCallNotice()
            }
        } else {
            refreshUI()
        }
    }

    private fun saveRecorderFile() {
        if (RecorderViewModelAction.hasInitRecorderService()) {
            RecorderViewModelAction.saveRecordInfo(saveRecordFromWhere = RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_APP_CARD)
        } else {
            refreshUI()
        }
    }

    override fun onReadyService() {
        localUnStandardSaveState = RecorderViewModelAction.saveFileState
        refreshUI()
    }

    override fun onCloseService() {
        showMarkNotice = false
        showNoMarkNotice = false
        showMuteNotice = false
        showDisConnectedCallNotice = false
        showConnectedCallNotice = false
        showInCallNotice = false
        refreshUI()
        cardUpdateCount = 0
    }

    override fun onWaveStateChange(state: Int) {
        refreshUI(state != RecorderViewModelAction.WaveState.UPDATE)
    }

    private fun refreshUI(forceUpdate: Boolean = true) {
        if (!FeatureOption.isHasSupportDragonfly()) return
        if (CardUtils.isDragonFlyVersionCode2OrLater()) {
            CardUtils.refreshDragonFlyCardData()
        } else {
            val size = widgetCodes.size
            if (size == 0) return
            if (forceUpdate || cardUpdateCount % REFRESH_RATE == 0) {
                val widgetIterator = widgetCodes.iterator()
                while (widgetIterator.hasNext()) {
                    widgetIterator.next()?.let {
                        postUpdateCommand(ctx, it)
                    }
                }
            }
            if (!forceUpdate) {
                cardUpdateCount++
            }
        }
    }

    override fun onSaveFileStateChange(
        state: Int,
        fileName: String,
        fullPath: String?,
        e: RecoverableSecurityException?
    ) {
        if (RecorderViewModelAction.SaveFileState.SUCCESS == state) {
            lastFileName = fileName
        }
        setLocalUnStandardSaveState(state)
        refreshUI()
    }

    private fun setLocalUnStandardSaveState(state: Int) {
        localUnStandardSaveState = state
        if (state == RecorderViewModelAction.SaveFileState.SUCCESS) {
            /**
             * 再这里通过delay改变本地localUnStandardSaveState，处理卡片能正常保存成功弹窗
             */
            mainHandler.removeCallbacksAndMessages(null)
            mainHandler.postDelayed({ // 与真实state保持一致，避免又重新开始录音
                DebugUtil.i(CARD_TAG, "run runnable")
                localUnStandardSaveState = RecorderViewModelAction.saveFileState
                refreshUI()
            }, DELAY_150)
        }
    }

    private fun getSaveFileState(): Int {
        return localUnStandardSaveState
    }

    override fun onRecordStatusChange(state: Int) {
        if ((state == RECORDING) && (RecorderViewModelAction.getLastStatus() != PAUSED) && (BaseApplication.sNeedToNormalRingMode)) {
            //开始录制调用
            checkShowMuteNotice()
        }
        refreshUI()
    }

    override fun onRecordCallConnected() {
        if (RecorderViewModelAction.getCurrentStatus() == PAUSED) {
            //来电暂停,断开通话后需要继续录制
            if (RecorderViewModelAction.isAudioModeChangePause() && RecorderViewModelAction.isNeedResume()) {
                checkShowConnectedCallNotice()
            }
        } else if (RecorderViewModelAction.getCurrentStatus() == RECORDING) {
            //通话中断后变为录制状态
            checkShowDisConnectedCallNotice()
        }
        refreshUI()
    }

    override fun onMarkDataChange(markAction: Int, errorOrIndex: Int) {
        if (markAction == RecorderViewModelAction.MarkAction.SINGLE_ADD || markAction == RecorderViewModelAction.MarkAction.MULTI_ADD) {
            checkShowMarkNotice()
        }
    }

    private fun checkShowMarkNotice() {
        showMarkNotice = true
        refreshUI()
        mainHandler.postDelayed({
            showMarkNotice = false
            refreshUI()
        }, DURATION_1500)
    }

    private fun checkShowNoMarkNotice() {
        showNoMarkNotice = true
        refreshUI()
        mainHandler.postDelayed({
            showNoMarkNotice = false
            refreshUI()
        }, DURATION_1500)
    }

    private fun checkShowDisConnectedCallNotice() {
        showDisConnectedCallNotice = true
        mainHandler.postDelayed({
            showDisConnectedCallNotice = false
            refreshUI()
        }, DURATION_1500)
    }

    private fun checkShowConnectedCallNotice() {
        showConnectedCallNotice = true
        mainHandler.postDelayed({
            showConnectedCallNotice = false
            refreshUI()
        }, DURATION_1500)
    }

    private fun checkShowMuteNotice() {
        showMuteNotice = true
        mainHandler.postDelayed({
            showMuteNotice = false
            refreshUI()
        }, DURATION_1500)
    }

    private fun checkShowInCallNotice() {
        showInCallNotice = true
        refreshUI()
        mainHandler.postDelayed({
            showInCallNotice = false
            refreshUI()
        }, DURATION_1500)
    }
}
