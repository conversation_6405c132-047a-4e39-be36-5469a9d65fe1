package oplus.multimedia.soundrecorder

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction

class RecorderStateProvider : ContentProvider() {

    companion object {
        const val TAG = "RecorderStateProvider"
        const val METHOD_RECORD_STATE = "method_record_state"
        const val SPEECH_ASSIST_PKG_NAME = "com.heytap.speechassist"
        const val NEW_SPEECH_ASSIST_PKG_NAME = "com.oplus.ai.assistant"
    }

    override fun onCreate(): Boolean {
        return true
    }

    override fun query(uri: Uri, projection: Array<out String>?, selection: String?, selectionArgs: Array<out String>?, sortOrder: String?): Cursor? {
        return null
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun update(uri: Uri, values: ContentValues?, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        DebugUtil.i(TAG, "---callmethod $method, caller = $callingPackage")
        if (!checkPackageAuth()) {
            DebugUtil.e(TAG, "package name not match")
            throw RuntimeException("package name not match")
        }
        when (method) {
            METHOD_RECORD_STATE -> {
                return Bundle().apply {
                    val state = RecorderViewModelAction.getCurrentStatus()
                    DebugUtil.i(TAG, "---call-record-state-- $state")
                    this.putInt("record_state", state)
                }
            }
        }
        return super.call(method, arg, extras)
    }

    private fun checkPackageAuth(): Boolean {
        val page = callingPackage
        return TextUtils.equals(page, SPEECH_ASSIST_PKG_NAME)
                || TextUtils.equals(page, NEW_SPEECH_ASSIST_PKG_NAME)
    }
}