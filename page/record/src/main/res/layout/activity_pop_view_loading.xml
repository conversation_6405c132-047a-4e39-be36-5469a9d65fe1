<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.coui.appcompat.dialog.widget.COUIDialogTitle
        android:id="@+id/alertTitle"
        style="@style/COUIDialogTextAppearance.Title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:ellipsize="end"
        android:gravity="center_horizontal|center"
        android:maxLines="1"
        android:text="@string/photo_mark_recommend_mark_adding" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:baselineAligned="false"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:weightSum="5">

        <View
            android:layout_width="0dp"
            android:layout_height="1px"
            android:layout_weight="1" />

        <com.coui.appcompat.progressbar.COUIHorizontalProgressBar
            android:id="@+id/progress"
            android:layout_width="@dimen/coui_loading_dialog_progress_width"
            android:layout_height="@dimen/coui_loading_dialog_progress_height"
            android:layout_weight="3"
            app:couiHorizontalProgressBarBackgroundColor="@color/coui_progress_spinner_background_color" />

        <View
            android:layout_width="0dp"
            android:layout_height="1px"
            android:layout_weight="1" />
    </LinearLayout>

    <Button
        android:id="@+id/btnCancel"
        style="@style/COUIAlertDialogBottomButton"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:paddingTop="0dp"
        android:paddingBottom="0dp"
        android:singleLine="false"
        android:text="@string/cancel" />
</androidx.appcompat.widget.LinearLayoutCompat>
