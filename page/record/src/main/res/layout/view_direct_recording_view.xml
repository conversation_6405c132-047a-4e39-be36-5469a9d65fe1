<?xml version="1.0" encoding="utf-8"?>

<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/record_enhance_linear"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/record_enhance_button_bg_off"
    android:orientation="horizontal"
    tools:ignore="MissingDefaultResource">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/record_enhance_img"
        android:layout_width="@dimen/dp22"
        android:layout_height="@dimen/dp22"
        android:layout_gravity="center_vertical"
        app:lottie_rawRes="@raw/anim_direct_on" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/record_enhance_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp6"
        android:text="@string/specified_direct_record_open"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/directional_button_text_color_default"
        android:textSize="@dimen/dp12" />
</merge>