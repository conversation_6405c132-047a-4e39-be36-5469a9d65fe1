<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:toos="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    toos:parentTag="android.widget.RelativeLayout">

    <ImageView
        android:id="@+id/buttonImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:scaleType="center"
        android:layout_gravity="center"
        android:contentDescription="@string/talkback_flag"
        toos:src="@drawable/selector_button_mark_enable" />

    <TextView
        android:id="@+id/buttonText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:maxWidth="@dimen/record_button_width"
        android:layout_below="@id/buttonImage"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp8"
        android:ellipsize="end"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/coui_color_label_secondary"
        android:textSize="@dimen/dp12"
        toos:text="@string/talkback_flag" />

    <!--@string/talkback_flag-->

</merge>