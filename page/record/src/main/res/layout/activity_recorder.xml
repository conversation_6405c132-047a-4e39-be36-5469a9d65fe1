<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/recorder_header"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/recorder_background_gradient"
        android:tag="sharedView"/>

    <!--该View仅用来适配底部 taskbar 颜色适配，若后续有新的方案可处理，可删除该View-->
    <View
        android:id="@+id/view_task_bar_navigation"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@color/background_taskbar_navigation"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/coui_color_background_with_card">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/abl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/coui_transparence"
            app:elevation="0dp"
            app:layout_constraintTop_toTopOf="parent">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                android:tag="toolBar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp52" />

        </com.google.android.material.appbar.AppBarLayout>

        <com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
            android:id="@+id/wave_gradient_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:tag="waveGradientView"
            app:layout_constraintHeight_percent="@dimen/record_wave_view_height_percent"
            android:layout_marginTop="@dimen/common_wave_view_margin_top"
            app:backgroundWhole="@color/wave_recycler_background"
            app:layout_constraintTop_toBottomOf="@id/abl">

            <com.soundrecorder.record.views.wave.RecorderWaveRecyclerView
                android:id="@+id/ruler_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:layoutDirection="ltr"
                android:tag="WaveRecyclerView" />
        </com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_start"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="@dimen/responsive_ui_margin_large" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_end"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_end="@dimen/responsive_ui_margin_large" />


        <FrameLayout
            android:id="@+id/middle_control"
            android:layout_width="@dimen/circle_record_button_diam"
            android:layout_height="@dimen/circle_record_button_diam"
            android:gravity="center"
            android:layout_marginBottom="@dimen/circle_record_button_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/guideline_end"
            app:layout_constraintStart_toStartOf="@id/guideline_start">

            <com.soundrecorder.record.views.RecorderAnimatedCircleButton
                android:id="@+id/red_circle_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:forceDarkAllowed="false"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_pause_icon"
                android:tag="sharedRedCircleView"
                app:circle_color="?attr/couiColorPrimary"
                app:circle_radius="@dimen/circle_record_button_radius" />

        </FrameLayout>

        <LinearLayout
            android:id="@+id/left_mark_layout"
            android:orientation="vertical"
            android:layout_width="@dimen/record_button_width"
            android:gravity="end"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/recorder_bottom_button_margin"
            android:layout_marginTop="@dimen/dp17"
            android:tag="markView"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintEnd_toStartOf="@id/middle_control"
            app:layout_constraintTop_toTopOf="@id/middle_control">

            <com.soundrecorder.record.views.CustomButtonView
                android:id="@+id/left_mark_control"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:background="@null"
                android:contentDescription="@string/talkback_flag"
                android:forceDarkAllowed="false"
                android:padding="@dimen/dp8"
                app:button_enable="true"
                app:button_light_img="@drawable/selector_button_mark"
                app:button_light_text_color="@color/coui_color_label_secondary"
                app:button_text="@string/talkback_flag"
                app:button_un_light_img="@drawable/selector_button_mark_unable"
                app:button_un_light_text_color="@color/coui_color_label_secondary" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ib_mark_photo_layout"
            android:layout_width="@dimen/record_button_width"
            android:orientation="vertical"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/recorder_bottom_button_margin"
            android:layout_marginTop="@dimen/dp17"
            android:tag="stopView"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintStart_toEndOf="@id/middle_control"
            app:layout_constraintTop_toTopOf="@id/middle_control">

            <com.soundrecorder.record.views.CustomButtonView
                android:id="@+id/ib_mark_photo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:background="@null"
                android:contentDescription="@string/picture_mark"
                android:forceDarkAllowed ="false"
                android:maxWidth="@dimen/record_button_width"
                android:padding="@dimen/dp8"
                app:button_enable="true"
                app:button_light_img="@drawable/selector_button_photo_mark"
                app:button_light_text_color="@color/coui_color_label_secondary"
                app:button_text="@string/picture_mark"
                app:button_un_light_img="@drawable/selector_button_photo_mark_unable"
                app:button_un_light_text_color="@color/coui_color_label_secondary" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/mark_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@id/markListViewBottom"
            app:layout_constraintEnd_toEndOf="@id/guideline_end"
            app:layout_constraintStart_toStartOf="@id/guideline_start"
            app:layout_constraintTop_toBottomOf="@id/recorder_top"
            app:layout_constraintWidth_percent="@dimen/screen_width_percent"
            android:paddingHorizontal="@dimen/recorder_marklist_padding_horizontal">

            <androidx.recyclerview.widget.COUIRecyclerView
                android:id="@+id/mark_listview"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scrollbars="none"
                android:visibility="invisible"
                tools:listitem="@layout/item_mark_list"
                tools:visibility="visible" />
        </LinearLayout>

        <View
            android:id="@+id/mark_list_footer_divider"
            android:layout_width="match_parent"
            app:layout_constraintTop_toBottomOf="@id/mark_layout"
            android:background="?attr/couiColorDivider"
            android:layout_height="1px"/>

        <Space
            android:id="@+id/markListViewBottom"
            app:layout_constraintBottom_toTopOf="@id/view_direct_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/record_mark_list_margin_bottom" />

        <Space
            android:id="@+id/view_center_divider"
            android:layout_width="1px"
            android:layout_height="@dimen/dp60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/recorder_top"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:tag="timerView"
            android:paddingVertical="@dimen/record_time_area_margin_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginHorizontal="@dimen/common_time_area_margin_horizontal"
            app:layout_constraintTop_toBottomOf="@id/wave_gradient_view">

            <TextView
                android:id="@+id/timerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/common_max_time_text_size"
                android:lines="1"
                android:gravity="center"
                android:padding="0dp"
                android:textColor="@color/coui_color_primary_neutral"
                android:textAppearance="@style/couiTextAppearanceDisplayM"
                android:fontFamily="sys-sans-en"
                android:fontFeatureSettings="tnum"
                android:textFontWeight="600"
                tools:text="00:11:22" />

            <TextView
                android:id="@+id/direct_record_tip_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/coui_color_label_secondary"
                android:textSize="@dimen/direct_record_tip_text_size"
                android:fontFamily="sans-serif-medium"
                android:visibility="invisible"
                android:text="@string/specified_direct_open_tips" />
        </LinearLayout>

        <com.soundrecorder.record.views.DirectRecordingView
            android:id="@+id/view_direct_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:forceDarkAllowed="false"
            app:layout_constraintBottom_toTopOf="@id/middle_control"
            app:layout_constraintEnd_toEndOf="@id/middle_control"
            app:layout_constraintStart_toStartOf="@id/middle_control"
            android:background="@drawable/record_enhance_button_bg_off"
            android:paddingVertical="@dimen/dp8"
            android:paddingStart="@dimen/dp12"
            android:paddingEnd="@dimen/dp14"
            android:layout_marginBottom="@dimen/dp24"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>