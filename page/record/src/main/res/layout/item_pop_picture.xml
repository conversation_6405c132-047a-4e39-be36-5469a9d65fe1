<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:alpha="1"
        android:descendantFocusability="blocksDescendants">

        <ImageView
            android:id="@+id/iv_pop_picture"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            tools:src="@drawable/ic_launcher_recorder"
            app:layout_constraintDimensionRatio="w,1:1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <com.coui.appcompat.checkbox.COUICheckBox
            android:id="@+id/checkbox_select"
            style="@style/Widget.COUI.CompoundButton.COUICheckBox.Shape"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp4"
            android:background="@null"
            android:clickable="false"
            android:paddingStart="0dp"
            android:paddingEnd="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>