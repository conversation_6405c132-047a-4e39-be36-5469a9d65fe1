<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="RecorderActivityTheme" parent="@style/AppNoTitleTheme">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:forceDarkAllowed" tools:targetApi="q">true</item>
        <item name="windowPreviewType">0</item>
    </style>

    <style name="NormalAndTransparentWindow" parent="AppBaseTheme.NoActionBar.ActionMode">
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="roundedCornerStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">4dp</item>
    </style>

    <style name="AppCardTheme" parent="@style/Theme.COUI.Dark">
        <item name="couiColorSurface">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
        <item name="windowActionBarOverlay">true</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@color/navigation_bar_transparent_color</item>
        <item name="android:enforceNavigationBarContrast" tools:targetApi="q">false</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
    </style>
</resources>