<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="CustomButtonView">
        <attr name="button_light_img" format="reference" />
        <attr name="button_un_light_img" format="reference" />
        <attr name="button_light_text_color" format="reference" />
        <attr name="button_un_light_text_color" format="reference" />
        <attr name="button_enable" format="boolean" />
        <attr name="button_text" format="string" />

    </declare-styleable>


    <style name="PopViewLoading" parent="@style/AppNoTitleTheme">
        <!--是否悬浮在activity上-->
        <item name="android:windowIsFloating">true</item>
        <!--透明是否-->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@drawable/coui_center_alert_dialog_bg</item>
        <item name="android:background">@null</item>
        <!--设置没有窗口标题、dialog标题等各种标题-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:title">@null</item>
        <item name="android:dialogTitle">@null</item>

    </style>
</resources>