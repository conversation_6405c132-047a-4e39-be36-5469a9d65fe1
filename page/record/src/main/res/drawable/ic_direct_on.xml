<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="22dp"
    android:height="22dp"
    android:viewportWidth="22"
    android:viewportHeight="22">
  <group>
    <clip-path
        android:pathData="M0,0h22v22h-22z"/>
    <path
        android:pathData="M11,11m-11,0a11,11 0,1 1,22 0a11,11 0,1 1,-22 0"
        android:fillColor="#EDEDED"/>
    <path
        android:pathData="M10.999,10.999m-8.538,0a8.538,8.538 0,1 1,17.077 0a8.538,8.538 0,1 1,-17.077 0"
        android:fillColor="#E0E0E0"/>
    <path
        android:pathData="M10.999,10.999m-8.447,0a8.447,8.447 0,1 1,16.893 0a8.447,8.447 0,1 1,-16.893 0"
        android:strokeAlpha="0.08"
        android:strokeWidth="0.183333"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M11,11m-5.527,0a5.527,5.527 0,1 1,11.054 0a5.527,5.527 0,1 1,-11.054 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="11"
            android:startY="5.473"
            android:endX="11"
            android:endY="16.527"
            android:type="linear">
          <item android:offset="0" android:color="#FFD9D9D9"/>
          <item android:offset="1" android:color="#FFCFCFCF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M11,11m-5.437,0a5.437,5.437 0,1 1,10.874 0a5.437,5.437 0,1 1,-10.874 0"
        android:strokeAlpha="0.15"
        android:strokeWidth="0.18"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M3.786,2.696L11,11L18.214,2.696C16.283,1.017 13.76,0 11,0C8.24,0 5.717,1.017 3.786,2.696Z"
        android:strokeAlpha="0.6"
        android:fillType="evenOdd"
        android:fillAlpha="0.6">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="11"
            android:startY="0"
            android:endX="11"
            android:endY="7.792"
            android:type="linear">
          <item android:offset="0" android:color="#FF4DE74D"/>
          <item android:offset="1" android:color="#FF34CE39"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M11,11m-2.75,0a2.75,2.75 0,1 1,5.5 0a2.75,2.75 0,1 1,-5.5 0"
        android:strokeWidth="1.1"
        android:strokeColor="#ffffff">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="10"
            android:startY="8.8"
            android:endX="11"
            android:endY="13.2"
            android:type="linear">
          <item android:offset="0" android:color="#FF3AD43D"/>
          <item android:offset="1" android:color="#FF2ECD32"/>
        </gradient>
      </aapt:attr>
    </path>

    <path
        android:pathData="M11,11m-2.75,0a2.75,2.75 0,1 1,5.5 0a2.75,2.75 0,1 1,-5.5 0"
        android:strokeWidth="1.1"
        android:strokeColor="#ffffff">
    </path>
  </group>
</vector>
